2025-06-02 10:45:03.989 | INFO     | utils.logging:setup_logging:153 | [PID:73781] Logging initialized - Level: INFO, File: logs/trading_agent_73781.log, Worker ID: 73781
2025-06-02 10:45:03.989 | INFO     | utils.logging:setup_logging:157 | [PID:73781] Worker logging setup complete - Worker ID: 73781
2025-06-02 10:45:03.989 | INFO     | trading.asymmetric_env:__init__:231 | [PID:73781] AsymmetricTradingEnv logger test message
2025-06-02 10:45:03.990 | INFO     | trading.asymmetric_env:__init__:241 | [PID:73781] AsymmetricTradingEnv __init__ (Log Level: INFO, Log File: logs/trading_agent.log, Console: True): Received tech_indicator_list (len: 34): ['SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26', 'RSI_14', 'CCI_20', 'MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'ADX_14', 'DMP_14', 'DMN_14', 'BBL_20_2.0', 'BBM_20_2.0', 'BBU_20_2.0', 'BBB_20_2.0', 'BBP_20_2.0', 'OBV', 'Price_Range', 'Price_Position', 'Returns_1d', 'Returns_5d', 'Returns_20d', 'Volume_MA_20', 'Volume_Ratio', 'Volatility_20d', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime_Numeric']
2025-06-02 10:45:03.990 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-02 10:45:03.990 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-02 10:45:03.990 | INFO     | trading.asymmetric_env:__init__:270 | [PID:73781] Dynamically calculated self.WARMUP_DAYS: 21
2025-06-02 10:45:03.991 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-02 10:45:03.991 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-02 10:45:03.992 | WARNING  | trading.asymmetric_env:__init__:340 | [PID:73781] AsymmetricTradingEnv STATE SPACE WARNING (enhanced_state_input=True): Input 'state_space' argument (376) is INCONSISTENT with environment's internal calculation. Based on harmonized stock_dim (10), tech_indicators, and asymmetric_features_size (50), the environment's true_original_state_space is 361, and its calculated enhanced_state_space should be 411. USING ENVIRONMENT'S CALCULATED enhanced_state_space (411) for the agent.
2025-06-02 10:45:03.992 | INFO     | trading.asymmetric_env:__init__:385 | [PID:73781] About to call super().__init__ with parameters:
2025-06-02 10:45:03.992 | INFO     | trading.asymmetric_env:__init__:386 |   df.shape: (17945, 46)
2025-06-02 10:45:03.992 | INFO     | trading.asymmetric_env:__init__:387 |   stock_dim: 10
2025-06-02 10:45:03.993 | INFO     | trading.asymmetric_env:__init__:388 |   hmax: 100
2025-06-02 10:45:03.993 | INFO     | trading.asymmetric_env:__init__:389 |   initial_amount: 100000.0
2025-06-02 10:45:03.993 | INFO     | trading.asymmetric_env:__init__:390 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
2025-06-02 10:45:03.993 | INFO     | trading.asymmetric_env:__init__:391 |   buy_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-02 10:45:03.993 | INFO     | trading.asymmetric_env:__init__:392 |   sell_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-02 10:45:03.993 | INFO     | trading.asymmetric_env:__init__:393 |   parent_init_state_space: 361
2025-06-02 10:45:03.993 | INFO     | trading.asymmetric_env:__init__:394 |   action_space: 10
2025-06-02 10:45:03.994 | INFO     | trading.asymmetric_env:__init__:395 |   lowercase_tech_indicator_list: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-02 10:45:03.994 | INFO     | trading.asymmetric_env:__init__:396 |   reward_scaling: 0.0001
2025-06-02 10:45:03.994 | INFO     | trading.asymmetric_env:__init__:399 | [PID:73781] Calling StockTradingEnv.__init__...
2025-06-02 10:45:03.998 | INFO     | trading.asymmetric_env:__init__:414 | [PID:73781] StockTradingEnv.__init__ completed successfully
2025-06-02 10:45:03.998 | INFO     | trading.asymmetric_env:__init__:425 | [PID:73781] Initialized consecutive_early_failures=0, max_consecutive_early_failures=5
2025-06-02 10:45:03.998 | INFO     | trading.asymmetric_env:__init__:429 | [PID:73781] Initialized self.critical_error_occurred to False.
2025-06-02 10:45:03.998 | INFO     | trading.asymmetric_env:__init__:435 | [PID:73781] DIMENSION FIX: Parent actually provides 326 dimensions, not 361
2025-06-02 10:45:03.999 | INFO     | trading.asymmetric_env:__init__:446 | [PID:73781] CORRECTED DIMENSIONS: original_state_space=326, enhanced_state_space=376
2025-06-02 10:45:03.999 | INFO     | trading.asymmetric_env:__init__:464 | AsymmetricTradingEnv initialized: stock_dim=10, enhanced_state_space=376, asymmetric_features_per_stock=5
2025-06-02 10:45:04.820 | INFO     | trading.asymmetric_env:reset:714 | [PID:73781] reset: Called. Current day: 0
2025-06-02 10:45:04.820 | INFO     | trading.asymmetric_env:reset:723 | [PID:73781] reset: Reset consecutive_early_failures to 0.
2025-06-02 10:45:04.820 | INFO     | trading.asymmetric_env:reset:730 | [PID:73781] reset: Explicitly set self.day to 0.
2025-06-02 10:45:04.823 | WARNING  | trading.asymmetric_env:reset:794 | [PID:73781] reset: parent_obs from super().reset() is not np.ndarray (type: <class 'list'>). Converting.
2025-06-02 10:45:04.823 | INFO     | trading.asymmetric_env:reset:805 | [PID:73781] reset: Completed. Enhanced state shape: (376,). Day is now 0. self.data is for day 2016-03-15 00:00:00
2025-06-02 10:45:04.832 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 0), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.833 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 1
2025-06-02 10:45:04.834 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 1
2025-06-02 10:45:04.835 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 1), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.835 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 2
2025-06-02 10:45:04.836 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 2
2025-06-02 10:45:04.837 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 2), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.838 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 3
2025-06-02 10:45:04.839 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 3
2025-06-02 10:45:04.840 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 3), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.840 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 4
2025-06-02 10:45:04.841 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 4
2025-06-02 10:45:04.842 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 4), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.842 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 5
2025-06-02 10:45:04.842 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 5
2025-06-02 10:45:04.843 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 5), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.843 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 6
2025-06-02 10:45:04.844 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 6
2025-06-02 10:45:04.845 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 6), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.845 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 7
2025-06-02 10:45:04.845 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 7
2025-06-02 10:45:04.846 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 7), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.846 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 8
2025-06-02 10:45:04.847 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 8
2025-06-02 10:45:04.848 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 8), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.848 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 9
2025-06-02 10:45:04.849 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 9
2025-06-02 10:45:04.850 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 9), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.851 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 10
2025-06-02 10:45:04.852 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 10
2025-06-02 10:45:04.853 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 10), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.854 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 11
2025-06-02 10:45:04.854 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 11
2025-06-02 10:45:04.856 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 11), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.856 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 12
2025-06-02 10:45:04.856 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 12
2025-06-02 10:45:04.857 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 12), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.858 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 13
2025-06-02 10:45:04.858 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 13
2025-06-02 10:45:04.859 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 13), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.859 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 14
2025-06-02 10:45:04.860 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 14
2025-06-02 10:45:04.861 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 14), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.861 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 15
2025-06-02 10:45:04.862 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 15
2025-06-02 10:45:04.863 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 15), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.863 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 16
2025-06-02 10:45:04.864 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 16
2025-06-02 10:45:04.865 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 16), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.865 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 17
2025-06-02 10:45:04.866 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 17
2025-06-02 10:45:04.867 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 17), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.867 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 18
2025-06-02 10:45:04.868 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 18
2025-06-02 10:45:04.869 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 18), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.870 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 19
2025-06-02 10:45:04.871 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 19
2025-06-02 10:45:04.872 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 19), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.873 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 20
2025-06-02 10:45:04.874 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73781] _create_warmup_response: Created safe response for warmup day 20
2025-06-02 10:45:04.875 | INFO     | trading.asymmetric_env:step:830 | [PID:73781] step: Warmup period (day 20), bypassing super().step() to prevent division by zero
2025-06-02 10:45:04.875 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73781] _create_warmup_response: Advanced day to 21
2025-06-02 10:45:04.876 | INFO     | trading.asymmetric_env:_create_warmup_response:1417 | [PID:73781] _create_warmup_response: Warmup completed after 21 days. Continuing with normal training.
2025-06-02 10:45:04.888 | ERROR    | trading.asymmetric_env:step:940 | [PID:73781] TypeError caught in step(). Error: 'NoneType' object is not subscriptable. super_step_return defined: True, value: None, type: <class 'NoneType'>. Day: 22, Actions: [-0.967266    0.65146774 -0.8322251   0.36256564  0.7683604  -0.3608323
 -0.49947944  0.94753593 -0.35238284  0.1507454 ]. Traceback:
Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 887, in step
    super_step_return = super().step(actions)
                        ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 343, in step
    end_total_asset = self.state[0] + sum(
                      ~~~~~~~~~~^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-02 10:45:04.888 | CRITICAL | trading.asymmetric_env:step:947 | [PID:73781] CRITICAL: Forcing termination due to TypeError from super().step() to prevent infinite loop. Day: 22
2025-06-02 10:45:04.888 | CRITICAL | trading.asymmetric_env:_create_fallback_response:1482 | [PID:73781] _create_fallback_response: CRITICAL ERROR DETECTED ('TypeError in step: 'NoneType' object is not subscriptable'). Setting self.critical_error_occurred to True.
2025-06-02 10:45:04.943 | WARNING  | trading.asymmetric_env:_calculate_asymmetric_features:660 | [PID:73781] _calculate_asymmetric_features: Insufficient data for AAPL on day 22 (len: 18 < self.WARMUP_DAYS (21)). Using default features.
2025-06-02 10:45:04.944 | ERROR    | trading.asymmetric_env:_create_fallback_response:1580 | [PID:73781] _create_fallback_response: TypeError in step: 'NoneType' object is not subscriptable. Info: {'error': "TypeError in step: 'NoneType' object is not subscriptable", 'day_at_call': 22, 'fallback_response': True, 'actions_at_fallback': [-0.9672660231590271, 0.6514677405357361, -0.8322250843048096, 0.36256563663482666, 0.7683603763580322, -0.3608323037624359, -0.4994794428348541, 0.9475359320640564, -0.3523828387260437, 0.15074540674686432], 'super_return_at_fallback_type': "<class 'NoneType'>", 'super_return_at_fallback_repr': 'None'}
2025-06-02 10:45:04.944 | INFO     | trading.asymmetric_env:reset:714 | [PID:73781] reset: Called. Current day: 22
2025-06-02 10:45:04.944 | CRITICAL | trading.asymmetric_env:reset:718 | [PID:73781] reset: Critical error occurred previously. Raising RuntimeError to halt training.
