2025-06-01 23:29:46.227 | INFO     | utils.logging:setup_logging:153 | [PID:26548] Logging initialized - Level: INFO, File: logs\trading_agent_26548.log, Worker ID: 26548
2025-06-01 23:29:46.228 | INFO     | utils.logging:setup_logging:157 | [PID:26548] Worker logging setup complete - Worker ID: 26548
2025-06-01 23:29:46.228 | INFO     | trading.asymmetric_env:__init__:231 | [PID:26548] AsymmetricTradingEnv logger test message
2025-06-01 23:29:46.229 | INFO     | trading.asymmetric_env:__init__:241 | [PID:26548] AsymmetricTradingEnv __init__ (Log Level: INFO, Log File: logs\trading_agent.log, Console: True): Received tech_indicator_list (len: 34): ['SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26', 'RSI_14', 'CCI_20', 'MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'ADX_14', 'DMP_14', 'DMN_14', 'BBL_20_2.0', 'BBM_20_2.0', 'BBU_20_2.0', 'BBB_20_2.0', 'BBP_20_2.0', 'OBV', 'Price_Range', 'Price_Position', 'Returns_1d', 'Returns_5d', 'Returns_20d', 'Volume_MA_20', 'Volume_Ratio', 'Volatility_20d', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime_Numeric']
2025-06-01 23:29:46.229 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-01 23:29:46.229 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-01 23:29:46.229 | INFO     | trading.asymmetric_env:__init__:270 | [PID:26548] Dynamically calculated self.WARMUP_DAYS: 21
2025-06-01 23:29:46.231 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-01 23:29:46.231 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-01 23:29:46.231 | WARNING  | trading.asymmetric_env:__init__:340 | [PID:26548] AsymmetricTradingEnv STATE SPACE WARNING (enhanced_state_input=True): Input 'state_space' argument (0) is INCONSISTENT with environment's internal calculation. Based on harmonized stock_dim (10), tech_indicators, and asymmetric_features_size (50), the environment's true_original_state_space is 361, and its calculated enhanced_state_space should be 411. USING ENVIRONMENT'S CALCULATED enhanced_state_space (411) for the agent.
2025-06-01 23:29:46.231 | INFO     | trading.asymmetric_env:__init__:385 | [PID:26548] About to call super().__init__ with parameters:
2025-06-01 23:29:46.232 | INFO     | trading.asymmetric_env:__init__:386 |   df.shape: (17945, 46)
2025-06-01 23:29:46.232 | INFO     | trading.asymmetric_env:__init__:387 |   stock_dim: 10
2025-06-01 23:29:46.241 | INFO     | trading.asymmetric_env:__init__:388 |   hmax: 100
2025-06-01 23:29:46.242 | INFO     | trading.asymmetric_env:__init__:389 |   initial_amount: 100000.0
2025-06-01 23:29:46.242 | INFO     | trading.asymmetric_env:__init__:390 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
2025-06-01 23:29:46.243 | INFO     | trading.asymmetric_env:__init__:391 |   buy_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-01 23:29:46.243 | INFO     | trading.asymmetric_env:__init__:392 |   sell_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-01 23:29:46.244 | INFO     | trading.asymmetric_env:__init__:393 |   parent_init_state_space: 361
2025-06-01 23:29:46.244 | INFO     | trading.asymmetric_env:__init__:394 |   action_space: 10
2025-06-01 23:29:46.245 | INFO     | trading.asymmetric_env:__init__:395 |   lowercase_tech_indicator_list: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-01 23:29:46.245 | INFO     | trading.asymmetric_env:__init__:396 |   reward_scaling: 0.0001
2025-06-01 23:29:46.246 | INFO     | trading.asymmetric_env:__init__:399 | [PID:26548] Calling StockTradingEnv.__init__...
2025-06-01 23:29:46.253 | INFO     | trading.asymmetric_env:__init__:414 | [PID:26548] StockTradingEnv.__init__ completed successfully
2025-06-01 23:29:46.253 | INFO     | trading.asymmetric_env:__init__:425 | [PID:26548] Initialized consecutive_early_failures=0, max_consecutive_early_failures=5
2025-06-01 23:29:46.253 | INFO     | trading.asymmetric_env:__init__:429 | [PID:26548] Initialized self.critical_error_occurred to False.
2025-06-01 23:29:46.254 | INFO     | trading.asymmetric_env:__init__:435 | [PID:26548] DIMENSION FIX: Parent actually provides 326 dimensions, not 361
2025-06-01 23:29:46.254 | INFO     | trading.asymmetric_env:__init__:446 | [PID:26548] CORRECTED DIMENSIONS: original_state_space=326, enhanced_state_space=376
2025-06-01 23:29:46.254 | INFO     | trading.asymmetric_env:__init__:464 | AsymmetricTradingEnv initialized: stock_dim=10, enhanced_state_space=376, asymmetric_features_per_stock=5
2025-06-01 23:29:46.258 | INFO     | __main__:train:937 | AsymmetricTradingEnv created successfully
2025-06-01 23:29:46.258 | INFO     | __main__:train:940 | About to reset training environment
2025-06-01 23:29:46.258 | INFO     | trading.asymmetric_env:reset:714 | [PID:26548] reset: Called. Current day: 0
2025-06-01 23:29:46.259 | INFO     | trading.asymmetric_env:reset:723 | [PID:26548] reset: Reset consecutive_early_failures to 0.
2025-06-01 23:29:46.259 | INFO     | trading.asymmetric_env:reset:730 | [PID:26548] reset: Explicitly set self.day to 0.
2025-06-01 23:29:46.265 | WARNING  | trading.asymmetric_env:reset:794 | [PID:26548] reset: parent_obs from super().reset() is not np.ndarray (type: <class 'list'>). Converting.
2025-06-01 23:29:46.265 | INFO     | trading.asymmetric_env:reset:805 | [PID:26548] reset: Completed. Enhanced state shape: (376,). Day is now 0. self.data is for day 2016-03-15 00:00:00
2025-06-01 23:29:46.266 | INFO     | __main__:train:942 | Training environment reset successfully
2025-06-01 23:29:46.266 | INFO     | __main__:train:943 | About to get state from training environment
2025-06-01 23:29:46.267 | INFO     | __main__:train:946 | Got actual state from training environment: dim=376
2025-06-01 23:29:46.267 | INFO     | __main__:train:951 | Train_env actual_state_dim after init: 376
2025-06-01 23:29:46.268 | INFO     | __main__:train:965 | About to create validation AsymmetricTradingEnv with config: stock_dim=10, action_space=10
2025-06-01 23:29:46.274 | INFO     | utils.logging:setup_logging:153 | [PID:26548] Logging initialized - Level: INFO, File: logs\trading_agent_26548.log, Worker ID: 26548
2025-06-01 23:29:46.275 | INFO     | utils.logging:setup_logging:157 | [PID:26548] Worker logging setup complete - Worker ID: 26548
2025-06-01 23:29:46.275 | INFO     | trading.asymmetric_env:__init__:231 | [PID:26548] AsymmetricTradingEnv logger test message
2025-06-01 23:29:46.275 | INFO     | trading.asymmetric_env:__init__:241 | [PID:26548] AsymmetricTradingEnv __init__ (Log Level: INFO, Log File: logs\trading_agent.log, Console: True): Received tech_indicator_list (len: 34): ['SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26', 'RSI_14', 'CCI_20', 'MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'ADX_14', 'DMP_14', 'DMN_14', 'BBL_20_2.0', 'BBM_20_2.0', 'BBU_20_2.0', 'BBB_20_2.0', 'BBP_20_2.0', 'OBV', 'Price_Range', 'Price_Position', 'Returns_1d', 'Returns_5d', 'Returns_20d', 'Volume_MA_20', 'Volume_Ratio', 'Volatility_20d', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime_Numeric']
2025-06-01 23:29:46.276 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-01 23:29:46.276 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-01 23:29:46.277 | INFO     | trading.asymmetric_env:__init__:270 | [PID:26548] Dynamically calculated self.WARMUP_DAYS: 21
2025-06-01 23:29:46.278 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-01 23:29:46.278 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-01 23:29:46.278 | WARNING  | trading.asymmetric_env:__init__:340 | [PID:26548] AsymmetricTradingEnv STATE SPACE WARNING (enhanced_state_input=True): Input 'state_space' argument (0) is INCONSISTENT with environment's internal calculation. Based on harmonized stock_dim (10), tech_indicators, and asymmetric_features_size (50), the environment's true_original_state_space is 361, and its calculated enhanced_state_space should be 411. USING ENVIRONMENT'S CALCULATED enhanced_state_space (411) for the agent.
2025-06-01 23:29:46.278 | INFO     | trading.asymmetric_env:__init__:385 | [PID:26548] About to call super().__init__ with parameters:
2025-06-01 23:29:46.279 | INFO     | trading.asymmetric_env:__init__:386 |   df.shape: (4490, 46)
2025-06-01 23:29:46.279 | INFO     | trading.asymmetric_env:__init__:387 |   stock_dim: 10
2025-06-01 23:29:46.279 | INFO     | trading.asymmetric_env:__init__:388 |   hmax: 100
2025-06-01 23:29:46.279 | INFO     | trading.asymmetric_env:__init__:389 |   initial_amount: 100000.0
2025-06-01 23:29:46.279 | INFO     | trading.asymmetric_env:__init__:390 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
2025-06-01 23:29:46.280 | INFO     | trading.asymmetric_env:__init__:391 |   buy_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-01 23:29:46.280 | INFO     | trading.asymmetric_env:__init__:392 |   sell_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-01 23:29:46.281 | INFO     | trading.asymmetric_env:__init__:393 |   parent_init_state_space: 361
2025-06-01 23:29:46.281 | INFO     | trading.asymmetric_env:__init__:394 |   action_space: 10
2025-06-01 23:29:46.281 | INFO     | trading.asymmetric_env:__init__:395 |   lowercase_tech_indicator_list: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-01 23:29:46.282 | INFO     | trading.asymmetric_env:__init__:396 |   reward_scaling: 0.0001
2025-06-01 23:29:46.282 | INFO     | trading.asymmetric_env:__init__:399 | [PID:26548] Calling StockTradingEnv.__init__...
2025-06-01 23:29:46.287 | INFO     | trading.asymmetric_env:__init__:414 | [PID:26548] StockTradingEnv.__init__ completed successfully
2025-06-01 23:29:46.287 | INFO     | trading.asymmetric_env:__init__:425 | [PID:26548] Initialized consecutive_early_failures=0, max_consecutive_early_failures=5
2025-06-01 23:29:46.288 | INFO     | trading.asymmetric_env:__init__:429 | [PID:26548] Initialized self.critical_error_occurred to False.
2025-06-01 23:29:46.288 | INFO     | trading.asymmetric_env:__init__:435 | [PID:26548] DIMENSION FIX: Parent actually provides 361 dimensions, not 361
2025-06-01 23:29:46.289 | INFO     | trading.asymmetric_env:__init__:446 | [PID:26548] CORRECTED DIMENSIONS: original_state_space=361, enhanced_state_space=411
2025-06-01 23:29:46.289 | INFO     | trading.asymmetric_env:__init__:464 | AsymmetricTradingEnv initialized: stock_dim=10, enhanced_state_space=411, asymmetric_features_per_stock=5
2025-06-01 23:29:46.291 | INFO     | __main__:train:967 | Validation AsymmetricTradingEnv created successfully
2025-06-01 23:29:46.292 | INFO     | __main__:train:970 | About to reset validation environment
2025-06-01 23:29:46.292 | INFO     | trading.asymmetric_env:reset:714 | [PID:26548] reset: Called. Current day: 0
2025-06-01 23:29:46.292 | INFO     | trading.asymmetric_env:reset:723 | [PID:26548] reset: Reset consecutive_early_failures to 0.
2025-06-01 23:29:46.293 | INFO     | trading.asymmetric_env:reset:730 | [PID:26548] reset: Explicitly set self.day to 0.
2025-06-01 23:29:46.297 | WARNING  | trading.asymmetric_env:reset:794 | [PID:26548] reset: parent_obs from super().reset() is not np.ndarray (type: <class 'list'>). Converting.
2025-06-01 23:29:46.298 | INFO     | trading.asymmetric_env:reset:805 | [PID:26548] reset: Completed. Enhanced state shape: (411,). Day is now 0. self.data is for day 2023-07-06 00:00:00
2025-06-01 23:29:46.298 | INFO     | __main__:train:972 | Validation environment reset successfully
2025-06-01 23:29:46.298 | INFO     | __main__:train:973 | About to get state from validation environment
2025-06-01 23:29:46.299 | INFO     | __main__:train:976 | Got actual state from validation environment: dim=411
2025-06-01 23:29:46.299 | INFO     | __main__:train:979 | Val_env actual_state_dim after init: 411
2025-06-01 23:29:46.300 | INFO     | __main__:train:986 | Environment created: state_dim=376, action_dim=10
2025-06-01 23:29:46.300 | INFO     | __main__:train:1003 | Creating SAC agent with state_dim=376, action_dim=10
2025-06-01 23:29:46.301 | INFO     | models.sac_agent:__init__:72 | SAC Agent initialized: state_dim=376, action_dim=10, device=cpu
2025-06-01 23:29:46.301 | INFO     | __main__:train:1028 | SAC agent created successfully
2025-06-01 23:29:46.302 | INFO     | __main__:train:1029 | Agent parameters: {'learning_rate': 0.0003, 'gamma': 0.99, 'tau': 0.005, 'alpha': 0.2, 'batch_size': 256, 'buffer_size': 1000000, 'net_dims': [128, 128], 'target_step': 10, 'repeat_times': 1.0, 'reward_scale': 1.0, 'if_per': False, 'if_off_policy': True, 'checkpoint_dir': 'models/checkpoints'}
2025-06-01 23:29:46.302 | INFO     | __main__:train:1052 | Starting training...
2025-06-01 23:29:46.302 | INFO     | models.sac_agent:_create_agent:79 | Creating SAC agent
2025-06-01 23:29:49.219 | INFO     | models.sac_agent:_create_agent:134 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-01 23:29:49.220 | INFO     | models.sac_agent:train:174 | Starting SAC training: 100000 timesteps
