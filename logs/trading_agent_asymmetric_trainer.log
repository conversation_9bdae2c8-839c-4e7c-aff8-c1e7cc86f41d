2025-06-03 20:18:46.039 | INFO     | utils.logging:setup_logging:153 | [PID:40304] Logging initialized - Level: INFO, File: logs\trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-03 20:18:46.040 | INFO     | utils.logging:setup_logging:157 | [PID:40304] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-03 20:18:46.041 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data\cache
2025-06-03 20:18:46.042 | INFO     | data.processor:__init__:36 | DataProcessor initialized with pandas_ta integration
2025-06-03 20:18:46.043 | INFO     | __main__:__init__:108 | AsymmetricTrainer initialized successfully
2025-06-03 20:18:46.043 | INFO     | __main__:__init__:109 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-03 20:18:46.044 | INFO     | __main__:__init__:110 | VIX integration enabled: True
2025-06-03 20:18:46.044 | INFO     | __main__:train:463 | Starting asymmetric trading strategy training...
2025-06-03 20:18:46.044 | INFO     | __main__:load_or_process_data:205 | Loading processed data from data\processed\processed_data.csv
2025-06-03 20:18:46.322 | INFO     | __main__:load_or_process_data:208 | Loaded 23370 records from processed file
2025-06-03 20:18:46.322 | INFO     | __main__:_validate_technical_indicators:238 | Expected 35 technical indicators
2025-06-03 20:18:46.322 | INFO     | __main__:_validate_technical_indicators:239 | Found 35 indicators in data
2025-06-03 20:18:46.361 | INFO     | __main__:_split_data:133 | Training data: 18700 records, 1870 unique dates
2025-06-03 20:18:46.363 | INFO     | __main__:_split_data:134 | Validation data: 4670 records, 467 unique dates
2025-06-03 20:18:46.419 | INFO     | __main__:prepare_training_data:336 | Applying NaN fill and type check for technical indicators in training data
2025-06-03 20:18:46.453 | INFO     | __main__:prepare_training_data:360 | Applying NaN fill and type check for technical indicators in validation data
2025-06-03 20:18:46.485 | INFO     | __main__:prepare_training_data:384 | Training data prepared: 18700 records
2025-06-03 20:18:46.486 | INFO     | __main__:prepare_training_data:385 | Validation data prepared: 4670 records
2025-06-03 20:18:46.489 | INFO     | __main__:create_asymmetric_environment:400 | Creating train environment...
2025-06-03 20:18:46.507 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 20:18:46.508 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 20:18:46.509 | INFO     | __main__:create_asymmetric_environment:446 | Train environment created successfully
2025-06-03 20:18:46.509 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 20:18:46.510 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 20:18:46.511 | INFO     | __main__:create_asymmetric_environment:400 | Creating validation environment...
2025-06-03 20:18:46.523 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 20:18:46.524 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 20:18:46.525 | INFO     | __main__:create_asymmetric_environment:446 | Validation environment created successfully
2025-06-03 20:18:46.525 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 20:18:46.526 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 20:18:46.528 | INFO     | __main__:train:496 | Training for 100000 timesteps
2025-06-03 20:18:46.528 | INFO     | __main__:_execute_training_loop:537 | Executing training loop...
2025-06-03 20:18:46.529 | INFO     | __main__:_execute_training_loop:540 | Creating SAC agent...
2025-06-03 20:18:46.531 | INFO     | models.sac_agent:__init__:72 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-03 20:18:46.531 | INFO     | __main__:_execute_training_loop:548 | Starting SAC training for 100000 timesteps
2025-06-03 20:18:46.532 | INFO     | models.sac_agent:_create_agent:79 | Creating SAC agent
2025-06-03 20:18:49.297 | INFO     | models.sac_agent:_create_agent:134 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-03 20:18:49.299 | INFO     | models.sac_agent:train:174 | Starting SAC training: 100000 timesteps
2025-06-03 20:21:47.052 | INFO     | utils.logging:setup_logging:153 | [PID:36108] Logging initialized - Level: INFO, File: logs\trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-03 20:21:47.054 | INFO     | utils.logging:setup_logging:157 | [PID:36108] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-03 20:21:47.065 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data\cache
2025-06-03 20:21:47.067 | INFO     | data.processor:__init__:36 | DataProcessor initialized with pandas_ta integration
2025-06-03 20:21:47.071 | INFO     | __main__:__init__:108 | AsymmetricTrainer initialized successfully
2025-06-03 20:21:47.074 | INFO     | __main__:__init__:109 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-03 20:21:47.077 | INFO     | __main__:__init__:110 | VIX integration enabled: True
2025-06-03 20:21:47.080 | INFO     | __main__:train:463 | Starting asymmetric trading strategy training...
2025-06-03 20:21:47.085 | INFO     | __main__:load_or_process_data:205 | Loading processed data from data\processed\processed_data.csv
2025-06-03 20:21:48.311 | INFO     | __main__:load_or_process_data:208 | Loaded 23370 records from processed file
2025-06-03 20:21:48.314 | INFO     | __main__:_validate_technical_indicators:238 | Expected 35 technical indicators
2025-06-03 20:21:48.317 | INFO     | __main__:_validate_technical_indicators:239 | Found 35 indicators in data
2025-06-03 20:21:48.475 | INFO     | __main__:_split_data:133 | Training data: 18700 records, 1870 unique dates
2025-06-03 20:21:48.481 | INFO     | __main__:_split_data:134 | Validation data: 4670 records, 467 unique dates
2025-06-03 20:21:48.694 | INFO     | __main__:prepare_training_data:336 | Applying NaN fill and type check for technical indicators in training data
2025-06-03 20:21:48.869 | INFO     | __main__:prepare_training_data:360 | Applying NaN fill and type check for technical indicators in validation data
2025-06-03 20:21:49.016 | INFO     | __main__:prepare_training_data:384 | Training data prepared: 18700 records
2025-06-03 20:21:49.019 | INFO     | __main__:prepare_training_data:385 | Validation data prepared: 4670 records
2025-06-03 20:21:49.025 | INFO     | __main__:create_asymmetric_environment:400 | Creating train environment...
2025-06-03 20:21:49.096 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 20:21:49.099 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 20:21:49.103 | INFO     | __main__:create_asymmetric_environment:446 | Train environment created successfully
2025-06-03 20:21:49.106 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 20:21:49.110 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 20:21:49.114 | INFO     | __main__:create_asymmetric_environment:400 | Creating validation environment...
2025-06-03 20:21:49.160 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 20:21:49.163 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 20:21:49.168 | INFO     | __main__:create_asymmetric_environment:446 | Validation environment created successfully
2025-06-03 20:21:49.172 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 20:21:49.175 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 20:21:49.182 | INFO     | __main__:train:496 | Training for 100000 timesteps
2025-06-03 20:21:49.185 | INFO     | __main__:_execute_training_loop:537 | Executing training loop...
2025-06-03 20:21:49.188 | INFO     | __main__:_execute_training_loop:540 | Creating SAC agent...
2025-06-03 20:21:49.196 | INFO     | models.sac_agent:__init__:72 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-03 20:21:49.201 | INFO     | __main__:_execute_training_loop:548 | Starting SAC training for 100000 timesteps
2025-06-03 20:21:49.204 | INFO     | models.sac_agent:_create_agent:79 | Creating SAC agent
2025-06-03 20:22:02.613 | INFO     | models.sac_agent:_create_agent:134 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-03 20:22:02.620 | INFO     | models.sac_agent:train:174 | Starting SAC training: 100000 timesteps
2025-06-03 20:26:58.345 | INFO     | utils.logging:setup_logging:153 | [PID:21584] Logging initialized - Level: INFO, File: logs\trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-03 20:26:58.348 | INFO     | utils.logging:setup_logging:157 | [PID:21584] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-03 20:26:58.359 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data\cache
2025-06-03 20:26:58.363 | INFO     | data.processor:__init__:36 | DataProcessor initialized with pandas_ta integration
2025-06-03 20:26:58.366 | INFO     | __main__:__init__:108 | AsymmetricTrainer initialized successfully
2025-06-03 20:26:58.369 | INFO     | __main__:__init__:109 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-03 20:26:58.372 | INFO     | __main__:__init__:110 | VIX integration enabled: True
2025-06-03 20:26:58.375 | INFO     | __main__:train:463 | Starting asymmetric trading strategy training...
2025-06-03 20:26:58.378 | INFO     | __main__:load_or_process_data:205 | Loading processed data from data\processed\processed_data.csv
2025-06-03 20:26:59.568 | INFO     | __main__:load_or_process_data:208 | Loaded 23370 records from processed file
2025-06-03 20:26:59.571 | INFO     | __main__:_validate_technical_indicators:238 | Expected 35 technical indicators
2025-06-03 20:26:59.574 | INFO     | __main__:_validate_technical_indicators:239 | Found 35 indicators in data
2025-06-03 20:26:59.716 | INFO     | __main__:_split_data:133 | Training data: 18700 records, 1870 unique dates
2025-06-03 20:26:59.721 | INFO     | __main__:_split_data:134 | Validation data: 4670 records, 467 unique dates
2025-06-03 20:26:59.943 | INFO     | __main__:prepare_training_data:336 | Applying NaN fill and type check for technical indicators in training data
2025-06-03 20:27:00.143 | INFO     | __main__:prepare_training_data:360 | Applying NaN fill and type check for technical indicators in validation data
2025-06-03 20:27:00.292 | INFO     | __main__:prepare_training_data:384 | Training data prepared: 18700 records
2025-06-03 20:27:00.295 | INFO     | __main__:prepare_training_data:385 | Validation data prepared: 4670 records
2025-06-03 20:27:00.301 | INFO     | __main__:create_asymmetric_environment:400 | Creating train environment...
2025-06-03 20:27:00.369 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 20:27:00.372 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 20:27:00.375 | INFO     | __main__:create_asymmetric_environment:446 | Train environment created successfully
2025-06-03 20:27:00.378 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 20:27:00.381 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 20:27:00.385 | INFO     | __main__:create_asymmetric_environment:400 | Creating validation environment...
2025-06-03 20:27:00.426 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 20:27:00.429 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 20:27:00.432 | INFO     | __main__:create_asymmetric_environment:446 | Validation environment created successfully
2025-06-03 20:27:00.435 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 20:27:00.438 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 20:27:00.444 | INFO     | __main__:train:496 | Training for 100000 timesteps
2025-06-03 20:27:00.448 | INFO     | __main__:_execute_training_loop:537 | Executing training loop...
2025-06-03 20:27:00.451 | INFO     | __main__:_execute_training_loop:540 | Creating SAC agent...
2025-06-03 20:27:00.455 | INFO     | models.sac_agent:__init__:72 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-03 20:27:00.460 | INFO     | __main__:_execute_training_loop:548 | Starting SAC training for 100000 timesteps
2025-06-03 20:27:00.463 | INFO     | models.sac_agent:_create_agent:79 | Creating SAC agent
2025-06-03 20:27:13.027 | INFO     | models.sac_agent:_create_agent:134 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-03 20:27:13.032 | INFO     | models.sac_agent:train:174 | Starting SAC training: 100000 timesteps
2025-06-03 20:30:57.616 | INFO     | utils.logging:setup_logging:153 | [PID:35804] Logging initialized - Level: INFO, File: logs\trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-03 20:30:57.616 | INFO     | utils.logging:setup_logging:157 | [PID:35804] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-03 20:30:57.618 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data\cache
2025-06-03 20:30:57.618 | INFO     | data.processor:__init__:36 | DataProcessor initialized with pandas_ta integration
2025-06-03 20:30:57.619 | INFO     | __main__:__init__:108 | AsymmetricTrainer initialized successfully
2025-06-03 20:30:57.619 | INFO     | __main__:__init__:109 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-03 20:30:57.620 | INFO     | __main__:__init__:110 | VIX integration enabled: True
2025-06-03 20:30:57.620 | INFO     | __main__:train:463 | Starting asymmetric trading strategy training...
2025-06-03 20:30:57.621 | INFO     | __main__:load_or_process_data:205 | Loading processed data from data\processed\processed_data.csv
2025-06-03 20:30:57.842 | INFO     | __main__:load_or_process_data:208 | Loaded 23370 records from processed file
2025-06-03 20:30:57.843 | INFO     | __main__:_validate_technical_indicators:238 | Expected 35 technical indicators
2025-06-03 20:30:57.843 | INFO     | __main__:_validate_technical_indicators:239 | Found 35 indicators in data
2025-06-03 20:30:57.872 | INFO     | __main__:_split_data:133 | Training data: 18700 records, 1870 unique dates
2025-06-03 20:30:57.874 | INFO     | __main__:_split_data:134 | Validation data: 4670 records, 467 unique dates
2025-06-03 20:30:57.916 | INFO     | __main__:prepare_training_data:336 | Applying NaN fill and type check for technical indicators in training data
2025-06-03 20:30:57.940 | INFO     | __main__:prepare_training_data:360 | Applying NaN fill and type check for technical indicators in validation data
2025-06-03 20:30:57.961 | INFO     | __main__:prepare_training_data:384 | Training data prepared: 18700 records
2025-06-03 20:30:57.962 | INFO     | __main__:prepare_training_data:385 | Validation data prepared: 4670 records
2025-06-03 20:30:57.963 | INFO     | __main__:create_asymmetric_environment:400 | Creating train environment...
2025-06-03 20:30:57.975 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 20:30:57.976 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 20:30:57.977 | INFO     | __main__:create_asymmetric_environment:446 | Train environment created successfully
2025-06-03 20:30:57.977 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 20:30:57.978 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 20:30:57.979 | INFO     | __main__:create_asymmetric_environment:400 | Creating validation environment...
2025-06-03 20:30:57.987 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 20:30:57.987 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 20:30:57.988 | INFO     | __main__:create_asymmetric_environment:446 | Validation environment created successfully
2025-06-03 20:30:57.988 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 20:30:57.989 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 20:30:57.990 | INFO     | __main__:train:496 | Training for 100000 timesteps
2025-06-03 20:30:57.990 | INFO     | __main__:_execute_training_loop:537 | Executing training loop...
2025-06-03 20:30:57.991 | INFO     | __main__:_execute_training_loop:540 | Creating SAC agent...
2025-06-03 20:30:57.991 | INFO     | models.sac_agent:__init__:72 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-03 20:30:57.992 | INFO     | __main__:_execute_training_loop:548 | Starting SAC training for 100000 timesteps
2025-06-03 20:30:57.992 | INFO     | models.sac_agent:_create_agent:79 | Creating SAC agent
2025-06-03 20:30:59.724 | INFO     | models.sac_agent:_create_agent:134 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-03 20:30:59.725 | INFO     | models.sac_agent:train:174 | Starting SAC training: 100000 timesteps
2025-06-03 20:33:52.616 | INFO     | utils.logging:setup_logging:153 | [PID:41064] Logging initialized - Level: INFO, File: logs\trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-03 20:33:52.620 | INFO     | utils.logging:setup_logging:157 | [PID:41064] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-03 20:33:52.628 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data\cache
2025-06-03 20:33:52.631 | INFO     | data.processor:__init__:36 | DataProcessor initialized with pandas_ta integration
2025-06-03 20:33:52.634 | INFO     | __main__:__init__:108 | AsymmetricTrainer initialized successfully
2025-06-03 20:33:52.636 | INFO     | __main__:__init__:109 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-03 20:33:52.639 | INFO     | __main__:__init__:110 | VIX integration enabled: True
2025-06-03 20:33:52.642 | INFO     | __main__:train:463 | Starting asymmetric trading strategy training...
2025-06-03 20:33:52.646 | INFO     | __main__:load_or_process_data:205 | Loading processed data from data\processed\processed_data.csv
2025-06-03 20:33:53.845 | INFO     | __main__:load_or_process_data:208 | Loaded 23370 records from processed file
2025-06-03 20:33:53.849 | INFO     | __main__:_validate_technical_indicators:238 | Expected 35 technical indicators
2025-06-03 20:33:53.852 | INFO     | __main__:_validate_technical_indicators:239 | Found 35 indicators in data
2025-06-03 20:33:54.002 | INFO     | __main__:_split_data:133 | Training data: 18700 records, 1870 unique dates
2025-06-03 20:33:54.007 | INFO     | __main__:_split_data:134 | Validation data: 4670 records, 467 unique dates
2025-06-03 20:33:54.198 | INFO     | __main__:prepare_training_data:336 | Applying NaN fill and type check for technical indicators in training data
2025-06-03 20:33:54.338 | INFO     | __main__:prepare_training_data:360 | Applying NaN fill and type check for technical indicators in validation data
2025-06-03 20:33:54.469 | INFO     | __main__:prepare_training_data:384 | Training data prepared: 18700 records
2025-06-03 20:33:54.471 | INFO     | __main__:prepare_training_data:385 | Validation data prepared: 4670 records
2025-06-03 20:33:54.478 | INFO     | __main__:create_asymmetric_environment:400 | Creating train environment...
2025-06-03 20:33:54.541 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 20:33:54.543 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 20:33:54.546 | INFO     | __main__:create_asymmetric_environment:446 | Train environment created successfully
2025-06-03 20:33:54.548 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 20:33:54.551 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 20:33:54.553 | INFO     | __main__:create_asymmetric_environment:400 | Creating validation environment...
2025-06-03 20:33:54.588 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 20:33:54.591 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 20:33:54.593 | INFO     | __main__:create_asymmetric_environment:446 | Validation environment created successfully
2025-06-03 20:33:54.596 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 20:33:54.599 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 20:33:54.603 | INFO     | __main__:train:496 | Training for 100000 timesteps
2025-06-03 20:33:54.606 | INFO     | __main__:_execute_training_loop:537 | Executing training loop...
2025-06-03 20:33:54.609 | INFO     | __main__:_execute_training_loop:540 | Creating SAC agent...
2025-06-03 20:33:54.612 | INFO     | models.sac_agent:__init__:72 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-03 20:33:54.615 | INFO     | __main__:_execute_training_loop:548 | Starting SAC training for 100000 timesteps
2025-06-03 20:33:54.618 | INFO     | models.sac_agent:_create_agent:79 | Creating SAC agent
2025-06-03 20:34:06.412 | INFO     | models.sac_agent:_create_agent:134 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-03 20:34:06.416 | INFO     | models.sac_agent:train:174 | Starting SAC training: 100000 timesteps
2025-06-03 23:34:29.963 | INFO     | utils.logging:setup_logging:153 | [PID:4861] Logging initialized - Level: INFO, File: logs/trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-03 23:34:29.963 | INFO     | utils.logging:setup_logging:157 | [PID:4861] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-03 23:34:29.964 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data/cache
2025-06-03 23:34:29.964 | INFO     | data.processor:__init__:36 | DataProcessor initialized with pandas_ta integration
2025-06-03 23:34:29.964 | INFO     | __main__:__init__:108 | AsymmetricTrainer initialized successfully
2025-06-03 23:34:29.964 | INFO     | __main__:__init__:109 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-03 23:34:29.964 | INFO     | __main__:__init__:110 | VIX integration enabled: True
2025-06-03 23:34:29.964 | INFO     | __main__:train:463 | Starting asymmetric trading strategy training...
2025-06-03 23:34:29.965 | INFO     | __main__:load_or_process_data:205 | Loading processed data from data/processed/processed_data.csv
2025-06-03 23:34:30.087 | INFO     | __main__:load_or_process_data:208 | Loaded 23370 records from processed file
2025-06-03 23:34:30.087 | INFO     | __main__:_validate_technical_indicators:238 | Expected 35 technical indicators
2025-06-03 23:34:30.087 | INFO     | __main__:_validate_technical_indicators:239 | Found 35 indicators in data
2025-06-03 23:34:30.100 | INFO     | __main__:_split_data:133 | Training data: 18700 records, 1870 unique dates
2025-06-03 23:34:30.100 | INFO     | __main__:_split_data:134 | Validation data: 4670 records, 467 unique dates
2025-06-03 23:34:30.118 | INFO     | __main__:prepare_training_data:336 | Applying NaN fill and type check for technical indicators in training data
2025-06-03 23:34:30.129 | INFO     | __main__:prepare_training_data:360 | Applying NaN fill and type check for technical indicators in validation data
2025-06-03 23:34:30.138 | INFO     | __main__:prepare_training_data:384 | Training data prepared: 18700 records
2025-06-03 23:34:30.138 | INFO     | __main__:prepare_training_data:385 | Validation data prepared: 4670 records
2025-06-03 23:34:30.138 | INFO     | __main__:create_asymmetric_environment:400 | Creating train environment...
2025-06-03 23:34:30.143 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 23:34:30.143 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 23:34:30.143 | INFO     | __main__:create_asymmetric_environment:446 | Train environment created successfully
2025-06-03 23:34:30.143 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 23:34:30.143 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 23:34:30.143 | INFO     | __main__:create_asymmetric_environment:400 | Creating validation environment...
2025-06-03 23:34:30.145 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 23:34:30.145 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 23:34:30.145 | INFO     | __main__:create_asymmetric_environment:446 | Validation environment created successfully
2025-06-03 23:34:30.145 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 23:34:30.145 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 23:34:30.147 | INFO     | __main__:train:496 | Training for 100000 timesteps
2025-06-03 23:34:30.147 | INFO     | __main__:_execute_training_loop:537 | Executing training loop...
2025-06-03 23:34:30.147 | INFO     | __main__:_execute_training_loop:540 | Creating SAC agent...
2025-06-03 23:34:30.147 | INFO     | models.sac_agent:__init__:72 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-03 23:34:30.147 | INFO     | __main__:_execute_training_loop:548 | Starting SAC training for 100000 timesteps
2025-06-03 23:34:30.148 | INFO     | models.sac_agent:_create_agent:79 | Creating SAC agent
2025-06-03 23:34:31.091 | INFO     | models.sac_agent:_create_agent:134 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-03 23:34:31.092 | INFO     | models.sac_agent:train:174 | Starting SAC training: 100000 timesteps
2025-06-03 23:57:55.530 | INFO     | utils.logging:setup_logging:153 | [PID:11995] Logging initialized - Level: INFO, File: logs/trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-03 23:57:55.530 | INFO     | utils.logging:setup_logging:157 | [PID:11995] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-03 23:57:55.531 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data/cache
2025-06-03 23:57:55.531 | INFO     | data.processor:__init__:36 | DataProcessor initialized with pandas_ta integration
2025-06-03 23:57:55.531 | INFO     | __main__:__init__:108 | AsymmetricTrainer initialized successfully
2025-06-03 23:57:55.531 | INFO     | __main__:__init__:109 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-03 23:57:55.531 | INFO     | __main__:__init__:110 | VIX integration enabled: True
2025-06-03 23:57:55.531 | INFO     | __main__:train:463 | Starting asymmetric trading strategy training...
2025-06-03 23:57:55.531 | INFO     | __main__:load_or_process_data:205 | Loading processed data from data/processed/processed_data.csv
2025-06-03 23:57:55.675 | INFO     | __main__:load_or_process_data:208 | Loaded 23370 records from processed file
2025-06-03 23:57:55.675 | INFO     | __main__:_validate_technical_indicators:238 | Expected 35 technical indicators
2025-06-03 23:57:55.675 | INFO     | __main__:_validate_technical_indicators:239 | Found 35 indicators in data
2025-06-03 23:57:55.689 | INFO     | __main__:_split_data:133 | Training data: 18700 records, 1870 unique dates
2025-06-03 23:57:55.690 | INFO     | __main__:_split_data:134 | Validation data: 4670 records, 467 unique dates
2025-06-03 23:57:55.710 | INFO     | __main__:prepare_training_data:336 | Applying NaN fill and type check for technical indicators in training data
2025-06-03 23:57:55.724 | INFO     | __main__:prepare_training_data:360 | Applying NaN fill and type check for technical indicators in validation data
2025-06-03 23:57:55.736 | INFO     | __main__:prepare_training_data:384 | Training data prepared: 18700 records
2025-06-03 23:57:55.736 | INFO     | __main__:prepare_training_data:385 | Validation data prepared: 4670 records
2025-06-03 23:57:55.737 | INFO     | __main__:create_asymmetric_environment:400 | Creating train environment...
2025-06-03 23:57:55.743 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 23:57:55.743 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 23:57:55.743 | INFO     | __main__:create_asymmetric_environment:446 | Train environment created successfully
2025-06-03 23:57:55.743 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 23:57:55.743 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 23:57:55.743 | INFO     | __main__:create_asymmetric_environment:400 | Creating validation environment...
2025-06-03 23:57:55.746 | INFO     | trading.asymmetric_env:__init__:205 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-03 23:57:55.746 | INFO     | trading.asymmetric_env:__init__:206 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-03 23:57:55.746 | INFO     | __main__:create_asymmetric_environment:446 | Validation environment created successfully
2025-06-03 23:57:55.746 | INFO     | __main__:create_asymmetric_environment:447 | State space: 401, Action space: 10
2025-06-03 23:57:55.747 | INFO     | __main__:create_asymmetric_environment:448 | Technical indicators: 35
2025-06-03 23:57:55.747 | INFO     | __main__:train:496 | Training for 100000 timesteps
2025-06-03 23:57:55.747 | INFO     | __main__:_execute_training_loop:537 | Executing training loop...
2025-06-03 23:57:55.748 | INFO     | __main__:_execute_training_loop:540 | Creating SAC agent...
2025-06-03 23:57:55.748 | INFO     | models.sac_agent:__init__:72 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-03 23:57:55.748 | INFO     | __main__:_execute_training_loop:548 | Starting SAC training for 100000 timesteps
2025-06-03 23:57:55.748 | INFO     | models.sac_agent:_create_agent:79 | Creating SAC agent
2025-06-03 23:57:56.682 | INFO     | models.sac_agent:_create_agent:134 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-03 23:57:56.683 | INFO     | models.sac_agent:train:174 | Starting SAC training: 100000 timesteps
2025-06-04 00:10:24.148 | INFO     | utils.logging:setup_logging:153 | [PID:14272] Logging initialized - Level: INFO, File: logs/trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-04 00:10:24.148 | INFO     | utils.logging:setup_logging:157 | [PID:14272] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-04 00:10:24.149 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-04 00:10:24.149 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-04 00:10:24.149 | INFO     | utils.logging:info:191 | AsymmetricTrainer initialized successfully
2025-06-04 00:10:24.149 | INFO     | utils.logging:info:191 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-04 00:10:24.149 | INFO     | utils.logging:info:191 | VIX integration enabled: True
2025-06-04 00:10:24.149 | INFO     | utils.logging:info:191 | Starting asymmetric trading strategy training...
2025-06-04 00:10:24.150 | INFO     | utils.logging:info:191 | Loading processed data from data/processed/processed_data.csv
2025-06-04 00:10:24.293 | INFO     | utils.logging:info:191 | Loaded 23370 records from processed file
2025-06-04 00:10:24.293 | INFO     | utils.logging:info:191 | Expected 35 technical indicators
2025-06-04 00:10:24.294 | INFO     | utils.logging:info:191 | Found 35 indicators in data
2025-06-04 00:10:24.307 | INFO     | utils.logging:info:191 | Training data: 18700 records, 1870 unique dates
2025-06-04 00:10:24.308 | INFO     | utils.logging:info:191 | Validation data: 4670 records, 467 unique dates
2025-06-04 00:10:24.328 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in training data
2025-06-04 00:10:24.340 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in validation data
2025-06-04 00:10:24.350 | INFO     | utils.logging:info:191 | Training data prepared: 18700 records
2025-06-04 00:10:24.350 | INFO     | utils.logging:info:191 | Validation data prepared: 4670 records
2025-06-04 00:10:24.350 | INFO     | utils.logging:info:191 | Creating train environment...
2025-06-04 00:10:24.355 | INFO     | utils.logging:info:191 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-04 00:10:24.355 | INFO     | utils.logging:info:191 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-04 00:10:24.355 | INFO     | utils.logging:info:191 | Train environment created successfully
2025-06-04 00:10:24.356 | INFO     | utils.logging:info:191 | State space: 401, Action space: 10
2025-06-04 00:10:24.356 | INFO     | utils.logging:info:191 | Technical indicators: 35
2025-06-04 00:10:24.356 | INFO     | utils.logging:info:191 | Creating validation environment...
2025-06-04 00:10:24.359 | INFO     | utils.logging:info:191 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-04 00:10:24.359 | INFO     | utils.logging:info:191 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-04 00:10:24.359 | INFO     | utils.logging:info:191 | Validation environment created successfully
2025-06-04 00:10:24.360 | INFO     | utils.logging:info:191 | State space: 401, Action space: 10
2025-06-04 00:10:24.360 | INFO     | utils.logging:info:191 | Technical indicators: 35
2025-06-04 00:10:24.360 | INFO     | utils.logging:info:191 | Training for 100000 timesteps
2025-06-04 00:10:24.360 | INFO     | utils.logging:info:191 | Executing training loop...
2025-06-04 00:10:24.360 | INFO     | utils.logging:info:191 | Creating SAC agent...
2025-06-04 00:10:24.361 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-04 00:10:24.361 | INFO     | utils.logging:info:191 | Starting SAC training for 100000 timesteps
2025-06-04 00:10:24.361 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-04 00:10:25.045 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-04 00:10:25.045 | INFO     | utils.logging:info:191 | Starting SAC training: 100000 timesteps
2025-06-04 10:56:57.224 | INFO     | utils.logging:setup_logging:153 | [PID:122566] Logging initialized - Level: INFO, File: logs/trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-04 10:56:57.225 | INFO     | utils.logging:setup_logging:157 | [PID:122566] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-04 10:56:57.225 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-04 10:56:57.226 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-04 10:56:57.226 | INFO     | utils.logging:info:191 | AsymmetricTrainer initialized successfully
2025-06-04 10:56:57.226 | INFO     | utils.logging:info:191 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-04 10:56:57.226 | INFO     | utils.logging:info:191 | VIX integration enabled: True
2025-06-04 10:56:57.226 | INFO     | utils.logging:info:191 | Starting asymmetric trading strategy training...
2025-06-04 10:56:57.226 | INFO     | utils.logging:info:191 | Loading processed data from data/processed/processed_data.csv
2025-06-04 10:56:57.365 | INFO     | utils.logging:info:191 | Loaded 23370 records from processed file
2025-06-04 10:56:57.365 | INFO     | utils.logging:info:191 | Expected 35 technical indicators
2025-06-04 10:56:57.366 | INFO     | utils.logging:info:191 | Found 35 indicators in data
2025-06-04 10:56:57.380 | INFO     | utils.logging:info:191 | Training data: 18700 records, 1870 unique dates
2025-06-04 10:56:57.380 | INFO     | utils.logging:info:191 | Validation data: 4670 records, 467 unique dates
2025-06-04 10:56:57.401 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in training data
2025-06-04 10:56:57.414 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in validation data
2025-06-04 10:56:57.424 | INFO     | utils.logging:info:191 | Training data prepared: 18700 records
2025-06-04 10:56:57.424 | INFO     | utils.logging:info:191 | Validation data prepared: 4670 records
2025-06-04 10:56:57.424 | INFO     | utils.logging:info:191 | Creating train environment...
2025-06-04 10:56:57.429 | INFO     | utils.logging:info:191 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-04 10:56:57.429 | INFO     | utils.logging:info:191 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-04 10:56:57.429 | INFO     | utils.logging:info:191 | Train environment created successfully
2025-06-04 10:56:57.430 | INFO     | utils.logging:info:191 | State space: 401, Action space: 10
2025-06-04 10:56:57.430 | INFO     | utils.logging:info:191 | Technical indicators: 35
2025-06-04 10:56:57.430 | INFO     | utils.logging:info:191 | Creating validation environment...
2025-06-04 10:56:57.432 | INFO     | utils.logging:info:191 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-04 10:56:57.432 | INFO     | utils.logging:info:191 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-04 10:56:57.433 | INFO     | utils.logging:info:191 | Validation environment created successfully
2025-06-04 10:56:57.433 | INFO     | utils.logging:info:191 | State space: 401, Action space: 10
2025-06-04 10:56:57.433 | INFO     | utils.logging:info:191 | Technical indicators: 35
2025-06-04 10:56:57.433 | INFO     | utils.logging:info:191 | Training for 100000 timesteps
2025-06-04 10:56:57.434 | INFO     | utils.logging:info:191 | Executing training loop...
2025-06-04 10:56:57.434 | INFO     | utils.logging:info:191 | Creating SAC agent...
2025-06-04 10:56:57.434 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-04 10:56:57.434 | INFO     | utils.logging:info:191 | Starting SAC training for 100000 timesteps
2025-06-04 10:56:57.434 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-04 10:56:58.462 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-04 10:56:58.463 | INFO     | utils.logging:info:191 | Starting SAC training: 100000 timesteps
2025-06-04 11:02:39.425 | INFO     | utils.logging:setup_logging:153 | [PID:123637] Logging initialized - Level: INFO, File: logs/trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-04 11:02:39.425 | INFO     | utils.logging:setup_logging:157 | [PID:123637] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-04 11:02:39.425 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-04 11:02:39.426 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-04 11:02:39.426 | INFO     | utils.logging:info:191 | AsymmetricTrainer initialized successfully
2025-06-04 11:02:39.426 | INFO     | utils.logging:info:191 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-04 11:02:39.426 | INFO     | utils.logging:info:191 | VIX integration enabled: True
2025-06-04 11:02:39.426 | INFO     | utils.logging:info:191 | Starting asymmetric trading strategy training...
2025-06-04 11:02:39.427 | INFO     | utils.logging:info:191 | Loading processed data from data/processed/processed_data.csv
2025-06-04 11:02:39.556 | INFO     | utils.logging:info:191 | Loaded 23370 records from processed file
2025-06-04 11:02:39.556 | INFO     | utils.logging:info:191 | Expected 35 technical indicators
2025-06-04 11:02:39.556 | INFO     | utils.logging:info:191 | Found 35 indicators in data
2025-06-04 11:02:39.568 | INFO     | utils.logging:info:191 | Training data: 18700 records, 1870 unique dates
2025-06-04 11:02:39.569 | INFO     | utils.logging:info:191 | Validation data: 4670 records, 467 unique dates
2025-06-04 11:02:39.588 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in training data
2025-06-04 11:02:39.598 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in validation data
2025-06-04 11:02:39.609 | INFO     | utils.logging:info:191 | Training data prepared: 18700 records
2025-06-04 11:02:39.609 | INFO     | utils.logging:info:191 | Validation data prepared: 4670 records
2025-06-04 11:02:39.609 | INFO     | utils.logging:info:191 | Creating train environment...
2025-06-04 11:02:39.613 | INFO     | utils.logging:info:191 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-04 11:02:39.613 | INFO     | utils.logging:info:191 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-04 11:02:39.613 | INFO     | utils.logging:info:191 | Train environment created successfully
2025-06-04 11:02:39.613 | INFO     | utils.logging:info:191 | State space: 401, Action space: 10
2025-06-04 11:02:39.613 | INFO     | utils.logging:info:191 | Technical indicators: 35
2025-06-04 11:02:39.613 | INFO     | utils.logging:info:191 | Creating validation environment...
2025-06-04 11:02:39.616 | INFO     | utils.logging:info:191 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-04 11:02:39.616 | INFO     | utils.logging:info:191 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-04 11:02:39.616 | INFO     | utils.logging:info:191 | Validation environment created successfully
2025-06-04 11:02:39.616 | INFO     | utils.logging:info:191 | State space: 401, Action space: 10
2025-06-04 11:02:39.616 | INFO     | utils.logging:info:191 | Technical indicators: 35
2025-06-04 11:02:39.617 | INFO     | utils.logging:info:191 | Training for 100000 timesteps
2025-06-04 11:02:39.617 | INFO     | utils.logging:info:191 | Executing training loop...
2025-06-04 11:02:39.617 | INFO     | utils.logging:info:191 | Creating SAC agent...
2025-06-04 11:02:39.617 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-04 11:02:39.618 | INFO     | utils.logging:info:191 | Starting SAC training for 100000 timesteps
2025-06-04 11:02:39.618 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-04 11:02:40.270 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-04 11:02:40.271 | INFO     | utils.logging:info:191 | Starting SAC training: 100000 timesteps
2025-06-04 11:31:20.806 | SUCCESS  | utils.logging:success:216 | SAC training completed: 100000 timesteps
2025-06-04 11:31:20.807 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 1721.189s
2025-06-04 11:31:20.807 | INFO     | utils.logging:info:191 | Model saved to checkpoints/asymmetric/final_model.pkl
2025-06-04 11:31:20.807 | INFO     | utils.logging:info:191 | Final model saved to: checkpoints/asymmetric/final_model.pkl
2025-06-04 11:31:20.808 | INFO     | utils.logging:info:191 | Training completed successfully!
2025-06-04 11:31:20.808 | INFO     | utils.logging:info:191 | Training completed successfully!
2025-06-04 11:31:20.809 | INFO     | utils.logging:info:191 | Starting model evaluation...
2025-06-04 11:31:20.809 | INFO     | utils.logging:info:191 | Loading processed data from data/processed/processed_data.csv
2025-06-04 11:31:20.958 | INFO     | utils.logging:info:191 | Loaded 23370 records from processed file
2025-06-04 11:31:20.958 | INFO     | utils.logging:info:191 | Expected 35 technical indicators
2025-06-04 11:31:20.959 | INFO     | utils.logging:info:191 | Found 35 indicators in data
2025-06-04 11:31:20.972 | INFO     | utils.logging:info:191 | Training data: 18700 records, 1870 unique dates
2025-06-04 11:31:20.973 | INFO     | utils.logging:info:191 | Validation data: 4670 records, 467 unique dates
2025-06-04 11:31:21.126 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in training data
2025-06-04 11:31:21.137 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in validation data
2025-06-04 11:31:21.147 | INFO     | utils.logging:info:191 | Training data prepared: 18700 records
2025-06-04 11:31:21.147 | INFO     | utils.logging:info:191 | Validation data prepared: 4670 records
2025-06-04 11:31:21.148 | INFO     | utils.logging:info:191 | Creating test environment...
2025-06-04 11:31:21.150 | INFO     | utils.logging:info:191 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-04 11:31:21.150 | INFO     | utils.logging:info:191 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-04 11:31:21.151 | INFO     | utils.logging:info:191 | Test environment created successfully
2025-06-04 11:31:21.151 | INFO     | utils.logging:info:191 | State space: 401, Action space: 10
2025-06-04 11:31:21.151 | INFO     | utils.logging:info:191 | Technical indicators: 35
2025-06-04 11:31:21.151 | INFO     | utils.logging:info:191 | Using trained agent for evaluation
2025-06-04 11:31:21.152 | INFO     | utils.logging:info:191 | Running evaluation episode 1/5
2025-06-04 11:31:21.155 | ERROR    | utils.logging:exception:206 | Error during evaluation: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (2,) + inhomogeneous part.
Traceback (most recent call last):

  File "/app/workspaces/finrl-bot/sonet/train_asymmetric.py", line 744, in <module>
    main()
    └ <function main at 0x75dfef47b920>

  File "/app/workspaces/finrl-bot/sonet/train_asymmetric.py", line 726, in main
    results = trainer.evaluate_model()
              │       └ <function AsymmetricTrainer.evaluate_model at 0x75dfab234a40>
              └ <__main__.AsymmetricTrainer object at 0x75dfab22da50>

> File "/app/workspaces/finrl-bot/sonet/train_asymmetric.py", line 656, in evaluate_model
    action = agent.predict(state, deterministic=True)
             │     │       └ (array([ 1.00000000e+05,  1.78779999e+02,  4.39029999e+02,  1.24250000e+02,
             │     │                 7.07396362e+02,  7.79080582e+01,  1.21558...
             │     └ <function SACAgent.predict at 0x75dfac4839c0>
             └ <models.sac_agent.SACAgent object at 0x75dfab073f50>

  File "/app/workspaces/finrl-bot/sonet/src/models/sac_agent.py", line 349, in predict
    state = np.array(state, dtype=np.float32)
            │  │     │            │  └ <class 'numpy.float32'>
            │  │     │            └ <module 'numpy' from '/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/numpy/__init__.py'>
            │  │     └ (array([ 1.00000000e+05,  1.78779999e+02,  4.39029999e+02,  1.24250000e+02,
            │  │               7.07396362e+02,  7.79080582e+01,  1.21558...
            │  └ <built-in function array>
            └ <module 'numpy' from '/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/numpy/__init__.py'>

ValueError: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (2,) + inhomogeneous part.
2025-06-04 11:31:21.165 | INFO     | utils.logging:info:191 | Final evaluation results: {}
2025-06-04 16:29:01.579 | INFO     | utils.logging:setup_logging:153 | [PID:167534] Logging initialized - Level: INFO, File: logs/trading_agent_asymmetric_trainer.log, Worker ID: asymmetric_trainer
2025-06-04 16:29:01.581 | INFO     | utils.logging:setup_logging:157 | [PID:167534] Worker logging setup complete - Worker ID: asymmetric_trainer
2025-06-04 16:29:01.586 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-04 16:29:01.588 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-04 16:29:01.589 | INFO     | utils.logging:info:191 | AsymmetricTrainer initialized successfully
2025-06-04 16:29:01.591 | INFO     | utils.logging:info:191 | Using 35 technical indicators: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12']...
2025-06-04 16:29:01.594 | INFO     | utils.logging:info:191 | VIX integration enabled: True
2025-06-04 16:29:01.595 | INFO     | utils.logging:info:191 | Starting asymmetric trading strategy training...
2025-06-04 16:29:01.599 | INFO     | utils.logging:info:191 | Loading processed data from data/processed/processed_data.csv
2025-06-04 16:29:03.073 | INFO     | utils.logging:info:191 | Loaded 23370 records from processed file
2025-06-04 16:29:03.075 | INFO     | utils.logging:info:191 | Expected 35 technical indicators
2025-06-04 16:29:03.077 | INFO     | utils.logging:info:191 | Found 35 indicators in data
2025-06-04 16:29:03.225 | INFO     | utils.logging:info:191 | Training data: 18700 records, 1870 unique dates
2025-06-04 16:29:03.230 | INFO     | utils.logging:info:191 | Validation data: 4670 records, 467 unique dates
2025-06-04 16:29:03.456 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in training data
2025-06-04 16:29:03.669 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in validation data
2025-06-04 16:29:03.812 | INFO     | utils.logging:info:191 | Training data prepared: 18700 records
2025-06-04 16:29:03.814 | INFO     | utils.logging:info:191 | Validation data prepared: 4670 records
2025-06-04 16:29:03.820 | INFO     | utils.logging:info:191 | Creating train environment...
2025-06-04 16:29:03.859 | INFO     | utils.logging:info:191 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-04 16:29:03.862 | INFO     | utils.logging:info:191 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-04 16:29:03.865 | INFO     | utils.logging:info:191 | Train environment created successfully
2025-06-04 16:29:03.870 | INFO     | utils.logging:info:191 | State space: 401, Action space: 10
2025-06-04 16:29:03.872 | INFO     | utils.logging:info:191 | Technical indicators: 35
2025-06-04 16:29:03.875 | INFO     | utils.logging:info:191 | Creating validation environment...
2025-06-04 16:29:03.912 | INFO     | utils.logging:info:191 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-04 16:29:03.914 | INFO     | utils.logging:info:191 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-04 16:29:03.918 | INFO     | utils.logging:info:191 | Validation environment created successfully
2025-06-04 16:29:03.919 | INFO     | utils.logging:info:191 | State space: 401, Action space: 10
2025-06-04 16:29:03.921 | INFO     | utils.logging:info:191 | Technical indicators: 35
2025-06-04 16:29:03.925 | INFO     | utils.logging:info:191 | Training for 100000 timesteps
2025-06-04 16:29:03.927 | INFO     | utils.logging:info:191 | Executing training loop...
2025-06-04 16:29:03.928 | INFO     | utils.logging:info:191 | Creating SAC agent...
2025-06-04 16:29:03.931 | INFO     | utils.logging:info:191 | SAC Agent initialized: state_dim=401, action_dim=10, device=cpu
2025-06-04 16:29:03.934 | INFO     | utils.logging:info:191 | Starting SAC training for 100000 timesteps
2025-06-04 16:29:03.935 | INFO     | utils.logging:info:191 | Creating SAC agent
2025-06-04 16:29:15.397 | INFO     | utils.logging:info:191 | SAC agent created successfully with params: {'net_dims': self.sac_config.get('net_dims', self.sac_config['actor_hidden_sizes']), 'state_dim': self.state_dim, 'action_dim': self.action_dim, 'gpu_id': gpu_id_to_pass, 'args': erl_config.__dict__}
2025-06-04 16:29:15.402 | INFO     | utils.logging:info:191 | Starting SAC training: 100000 timesteps
2025-06-04 17:57:35.303 | SUCCESS  | utils.logging:success:216 | SAC training completed: 100000 timesteps
2025-06-04 17:57:35.334 | INFO     | utils.logging:wrapper:392 | Performance - SACAgent.train: 5311.398s
2025-06-04 17:57:35.354 | INFO     | utils.logging:info:191 | Model saved to checkpoints/asymmetric/final_model.pkl
2025-06-04 17:57:35.355 | INFO     | utils.logging:info:191 | Final model saved to: checkpoints/asymmetric/final_model.pkl
2025-06-04 17:57:35.356 | INFO     | utils.logging:info:191 | Training completed successfully!
2025-06-04 17:57:35.420 | INFO     | utils.logging:info:191 | Training completed successfully!
2025-06-04 17:57:35.421 | INFO     | utils.logging:info:191 | Starting model evaluation...
2025-06-04 17:57:35.425 | INFO     | utils.logging:info:191 | Loading processed data from data/processed/processed_data.csv
2025-06-04 17:57:36.443 | INFO     | utils.logging:info:191 | Loaded 23370 records from processed file
2025-06-04 17:57:36.445 | INFO     | utils.logging:info:191 | Expected 35 technical indicators
2025-06-04 17:57:36.446 | INFO     | utils.logging:info:191 | Found 35 indicators in data
2025-06-04 17:57:36.591 | INFO     | utils.logging:info:191 | Training data: 18700 records, 1870 unique dates
2025-06-04 17:57:36.593 | INFO     | utils.logging:info:191 | Validation data: 4670 records, 467 unique dates
2025-06-04 17:57:40.202 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in training data
2025-06-04 17:57:40.251 | INFO     | utils.logging:info:191 | Applying NaN fill and type check for technical indicators in validation data
2025-06-04 17:57:40.279 | INFO     | utils.logging:info:191 | Training data prepared: 18700 records
2025-06-04 17:57:40.280 | INFO     | utils.logging:info:191 | Validation data prepared: 4670 records
2025-06-04 17:57:40.281 | INFO     | utils.logging:info:191 | Creating test environment...
2025-06-04 17:57:40.295 | INFO     | utils.logging:info:191 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-04 17:57:40.295 | INFO     | utils.logging:info:191 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-04 17:57:40.296 | INFO     | utils.logging:info:191 | Test environment created successfully
2025-06-04 17:57:40.297 | INFO     | utils.logging:info:191 | State space: 401, Action space: 10
2025-06-04 17:57:40.297 | INFO     | utils.logging:info:191 | Technical indicators: 35
2025-06-04 17:57:40.298 | INFO     | utils.logging:info:191 | Using trained agent for evaluation
2025-06-04 17:57:40.299 | INFO     | utils.logging:info:191 | Running evaluation episode 1/5
2025-06-04 17:57:40.369 | ERROR    | utils.logging:exception:206 | Error during evaluation: mat1 and mat2 shapes cannot be multiplied (1x431 and 401x128)
Traceback (most recent call last):

  File "/app/workspaces/finrl-bot/sonet/train_asymmetric.py", line 744, in <module>
    main()
    └ <function main at 0x7bd85187b920>

  File "/app/workspaces/finrl-bot/sonet/train_asymmetric.py", line 726, in main
    results = trainer.evaluate_model()
              │       └ <function AsymmetricTrainer.evaluate_model at 0x7bd80d5d0a40>
              └ <__main__.AsymmetricTrainer object at 0x7bd80d5c9d50>

> File "/app/workspaces/finrl-bot/sonet/train_asymmetric.py", line 656, in evaluate_model
    action = agent.predict(state, deterministic=True)
             │     │       └ array([ 1.00000000e+05,  1.78780014e+02,  4.39029999e+02,  1.24250000e+02,
             │     │                 7.08857605e+02,  7.79080505e+01,  1.215588...
             │     └ <function SACAgent.predict at 0x7bd80e8779c0>
             └ <models.sac_agent.SACAgent object at 0x7bd80cc66c10>

  File "/app/workspaces/finrl-bot/sonet/src/models/sac_agent.py", line 360, in predict
    action = self.agent.act(state_tensor)
             │    │     │   └ tensor([[ 1.0000e+05,  1.7878e+02,  4.3903e+02,  1.2425e+02,  7.0886e+02,
             │    │     │               7.7908e+01,  1.2156e+02,  2.6334e+02,  3....
             │    │     └ ActorSAC(
             │    │         (net_s): Sequential(
             │    │           (0): Linear(in_features=401, out_features=128, bias=True)
             │    │           (1): GELU(approximate='non...
             │    └ <elegantrl.agents.AgentSAC.AgentSAC object at 0x7bd80cc72e50>
             └ <models.sac_agent.SACAgent object at 0x7bd80cc66c10>

  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           │    │           │       └ {}
           │    │           └ (tensor([[ 1.0000e+05,  1.7878e+02,  4.3903e+02,  1.2425e+02,  7.0886e+02,
           │    │                       7.7908e+01,  1.2156e+02,  2.6334e+02,  3...
           │    └ <function Module._call_impl at 0x7bd82090aca0>
           └ ActorSAC(
               (net_s): Sequential(
                 (0): Linear(in_features=401, out_features=128, bias=True)
                 (1): GELU(approximate='non...
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
           │             │       └ {}
           │             └ (tensor([[ 1.0000e+05,  1.7878e+02,  4.3903e+02,  1.2425e+02,  7.0886e+02,
           │                         7.7908e+01,  1.2156e+02,  2.6334e+02,  3...
           └ <bound method ActorSAC.forward of ActorSAC(
               (net_s): Sequential(
                 (0): Linear(in_features=401, out_features=128, bias=Tr...
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/elegantrl/agents/AgentSAC.py", line 175, in forward
    s_enc = self.net_s(state)  # encoded state
            │          └ tensor([[ 1.0000e+05,  1.7878e+02,  4.3903e+02,  1.2425e+02,  7.0886e+02,
            │                      7.7908e+01,  1.2156e+02,  2.6334e+02,  3....
            └ ActorSAC(
                (net_s): Sequential(
                  (0): Linear(in_features=401, out_features=128, bias=True)
                  (1): GELU(approximate='non...
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           │    │           │       └ {}
           │    │           └ (tensor([[ 1.0000e+05,  1.7878e+02,  4.3903e+02,  1.2425e+02,  7.0886e+02,
           │    │                       7.7908e+01,  1.2156e+02,  2.6334e+02,  3...
           │    └ <function Module._call_impl at 0x7bd82090aca0>
           └ Sequential(
               (0): Linear(in_features=401, out_features=128, bias=True)
               (1): GELU(approximate='none')
               (2): Linear(in_feat...
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
           │             │       └ {}
           │             └ (tensor([[ 1.0000e+05,  1.7878e+02,  4.3903e+02,  1.2425e+02,  7.0886e+02,
           │                         7.7908e+01,  1.2156e+02,  2.6334e+02,  3...
           └ <bound method Sequential.forward of Sequential(
               (0): Linear(in_features=401, out_features=128, bias=True)
               (1): GELU(appro...
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/torch/nn/modules/container.py", line 250, in forward
    input = module(input)
            │      └ tensor([[ 1.0000e+05,  1.7878e+02,  4.3903e+02,  1.2425e+02,  7.0886e+02,
            │                  7.7908e+01,  1.2156e+02,  2.6334e+02,  3....
            └ Linear(in_features=401, out_features=128, bias=True)
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           │    │           │       └ {}
           │    │           └ (tensor([[ 1.0000e+05,  1.7878e+02,  4.3903e+02,  1.2425e+02,  7.0886e+02,
           │    │                       7.7908e+01,  1.2156e+02,  2.6334e+02,  3...
           │    └ <function Module._call_impl at 0x7bd82090aca0>
           └ Linear(in_features=401, out_features=128, bias=True)
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
           │             │       └ {}
           │             └ (tensor([[ 1.0000e+05,  1.7878e+02,  4.3903e+02,  1.2425e+02,  7.0886e+02,
           │                         7.7908e+01,  1.2156e+02,  2.6334e+02,  3...
           └ <bound method Linear.forward of Linear(in_features=401, out_features=128, bias=True)>
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/torch/nn/modules/linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
           │ │      │      │            └ Linear(in_features=401, out_features=128, bias=True)
           │ │      │      └ Linear(in_features=401, out_features=128, bias=True)
           │ │      └ tensor([[ 1.0000e+05,  1.7878e+02,  4.3903e+02,  1.2425e+02,  7.0886e+02,
           │ │                  7.7908e+01,  1.2156e+02,  2.6334e+02,  3....
           │ └ <built-in function linear>
           └ <module 'torch.nn.functional' from '/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/torch/nn/fu...

RuntimeError: mat1 and mat2 shapes cannot be multiplied (1x431 and 401x128)
2025-06-04 17:57:40.520 | INFO     | utils.logging:info:191 | Final evaluation results: {}
