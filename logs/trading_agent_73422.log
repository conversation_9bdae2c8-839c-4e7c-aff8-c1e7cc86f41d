2025-06-02 10:43:55.693 | INFO     | utils.logging:setup_logging:153 | [PID:73422] Logging initialized - Level: INFO, File: logs/trading_agent_73422.log, Worker ID: 73422
2025-06-02 10:43:55.693 | INFO     | utils.logging:setup_logging:157 | [PID:73422] Worker logging setup complete - Worker ID: 73422
2025-06-02 10:43:55.694 | INFO     | trading.asymmetric_env:__init__:231 | [PID:73422] AsymmetricTradingEnv logger test message
2025-06-02 10:43:55.694 | INFO     | trading.asymmetric_env:__init__:241 | [PID:73422] AsymmetricTradingEnv __init__ (Log Level: INFO, Log File: logs/trading_agent.log, Console: True): Received tech_indicator_list (len: 34): ['SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26', 'RSI_14', 'CCI_20', 'MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'ADX_14', 'DMP_14', 'DMN_14', 'BBL_20_2.0', 'BBM_20_2.0', 'BBU_20_2.0', 'BBB_20_2.0', 'BBP_20_2.0', 'OBV', 'Price_Range', 'Price_Position', 'Returns_1d', 'Returns_5d', 'Returns_20d', 'Volume_MA_20', 'Volume_Ratio', 'Volatility_20d', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime_Numeric']
2025-06-02 10:43:55.694 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-02 10:43:55.695 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-02 10:43:55.695 | INFO     | trading.asymmetric_env:__init__:270 | [PID:73422] Dynamically calculated self.WARMUP_DAYS: 21
2025-06-02 10:43:55.695 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-02 10:43:55.695 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-02 10:43:55.695 | INFO     | trading.asymmetric_env:__init__:385 | [PID:73422] About to call super().__init__ with parameters:
2025-06-02 10:43:55.696 | INFO     | trading.asymmetric_env:__init__:386 |   df.shape: (2510, 66)
2025-06-02 10:43:55.696 | INFO     | trading.asymmetric_env:__init__:387 |   stock_dim: 10
2025-06-02 10:43:55.696 | INFO     | trading.asymmetric_env:__init__:388 |   hmax: 100
2025-06-02 10:43:55.696 | INFO     | trading.asymmetric_env:__init__:389 |   initial_amount: 100000.0
2025-06-02 10:43:55.696 | INFO     | trading.asymmetric_env:__init__:390 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
2025-06-02 10:43:55.697 | INFO     | trading.asymmetric_env:__init__:391 |   buy_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-02 10:43:55.697 | INFO     | trading.asymmetric_env:__init__:392 |   sell_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-02 10:43:55.697 | INFO     | trading.asymmetric_env:__init__:393 |   parent_init_state_space: 361
2025-06-02 10:43:55.697 | INFO     | trading.asymmetric_env:__init__:394 |   action_space: 10
2025-06-02 10:43:55.698 | INFO     | trading.asymmetric_env:__init__:395 |   lowercase_tech_indicator_list: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-02 10:43:55.698 | INFO     | trading.asymmetric_env:__init__:396 |   reward_scaling: 0.0001
2025-06-02 10:43:55.698 | INFO     | trading.asymmetric_env:__init__:399 | [PID:73422] Calling StockTradingEnv.__init__...
2025-06-02 10:43:55.699 | ERROR    | trading.asymmetric_env:__init__:416 | [PID:73422] Error in StockTradingEnv.__init__: 'numpy.float64' object has no attribute 'values'
2025-06-02 10:43:55.699 | ERROR    | trading.asymmetric_env:__init__:417 | [PID:73422] Exception type: AttributeError
2025-06-02 10:43:55.700 | ERROR    | trading.asymmetric_env:__init__:419 | [PID:73422] Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 400, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 76, in __init__
    self.state = self._initiate_state()
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 409, in _initiate_state
    + self.data.close.values.tolist()
      ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'numpy.float64' object has no attribute 'values'

2025-06-02 10:43:55.700 | ERROR    | backtesting.engine:run_backtest:99 | Backtest failed: 'numpy.float64' object has no attribute 'values'
2025-06-02 10:43:55.701 | ERROR    | __main__:backtest:1111 | Backtesting failed: 'numpy.float64' object has no attribute 'values'
