2025-06-02 10:45:01.168 | INFO     | utils.logging:setup_logging:153 | [PID:73758] Logging initialized - Level: INFO, File: logs/trading_agent_73758.log, Worker ID: 73758
2025-06-02 10:45:01.168 | INFO     | utils.logging:setup_logging:157 | [PID:73758] Worker logging setup complete - Worker ID: 73758
2025-06-02 10:45:01.168 | INFO     | trading.asymmetric_env:__init__:231 | [PID:73758] AsymmetricTradingEnv logger test message
2025-06-02 10:45:01.168 | INFO     | trading.asymmetric_env:__init__:241 | [PID:73758] AsymmetricTradingEnv __init__ (Log Level: INFO, Log File: logs/trading_agent.log, Console: True): Received tech_indicator_list (len: 34): ['SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26', 'RSI_14', 'CCI_20', 'MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'ADX_14', 'DMP_14', 'DMN_14', 'BBL_20_2.0', 'BBM_20_2.0', 'BBU_20_2.0', 'BBB_20_2.0', 'BBP_20_2.0', 'OBV', 'Price_Range', 'Price_Position', 'Returns_1d', 'Returns_5d', 'Returns_20d', 'Volume_MA_20', 'Volume_Ratio', 'Volatility_20d', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime_Numeric']
2025-06-02 10:45:01.169 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-02 10:45:01.169 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-02 10:45:01.169 | INFO     | trading.asymmetric_env:__init__:270 | [PID:73758] Dynamically calculated self.WARMUP_DAYS: 21
2025-06-02 10:45:01.169 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-02 10:45:01.170 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-02 10:45:01.170 | WARNING  | trading.asymmetric_env:__init__:340 | [PID:73758] AsymmetricTradingEnv STATE SPACE WARNING (enhanced_state_input=True): Input 'state_space' argument (376) is INCONSISTENT with environment's internal calculation. Based on harmonized stock_dim (10), tech_indicators, and asymmetric_features_size (50), the environment's true_original_state_space is 361, and its calculated enhanced_state_space should be 411. USING ENVIRONMENT'S CALCULATED enhanced_state_space (411) for the agent.
2025-06-02 10:45:01.170 | INFO     | trading.asymmetric_env:__init__:385 | [PID:73758] About to call super().__init__ with parameters:
2025-06-02 10:45:01.170 | INFO     | trading.asymmetric_env:__init__:386 |   df.shape: (17945, 46)
2025-06-02 10:45:01.170 | INFO     | trading.asymmetric_env:__init__:387 |   stock_dim: 10
2025-06-02 10:45:01.170 | INFO     | trading.asymmetric_env:__init__:388 |   hmax: 100
2025-06-02 10:45:01.171 | INFO     | trading.asymmetric_env:__init__:389 |   initial_amount: 100000.0
2025-06-02 10:45:01.171 | INFO     | trading.asymmetric_env:__init__:390 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
2025-06-02 10:45:01.171 | INFO     | trading.asymmetric_env:__init__:391 |   buy_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-02 10:45:01.171 | INFO     | trading.asymmetric_env:__init__:392 |   sell_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-02 10:45:01.172 | INFO     | trading.asymmetric_env:__init__:393 |   parent_init_state_space: 361
2025-06-02 10:45:01.172 | INFO     | trading.asymmetric_env:__init__:394 |   action_space: 10
2025-06-02 10:45:01.172 | INFO     | trading.asymmetric_env:__init__:395 |   lowercase_tech_indicator_list: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-02 10:45:01.172 | INFO     | trading.asymmetric_env:__init__:396 |   reward_scaling: 0.0001
2025-06-02 10:45:01.172 | INFO     | trading.asymmetric_env:__init__:399 | [PID:73758] Calling StockTradingEnv.__init__...
2025-06-02 10:45:01.175 | INFO     | trading.asymmetric_env:__init__:414 | [PID:73758] StockTradingEnv.__init__ completed successfully
2025-06-02 10:45:01.176 | INFO     | trading.asymmetric_env:__init__:425 | [PID:73758] Initialized consecutive_early_failures=0, max_consecutive_early_failures=5
2025-06-02 10:45:01.176 | INFO     | trading.asymmetric_env:__init__:429 | [PID:73758] Initialized self.critical_error_occurred to False.
2025-06-02 10:45:01.176 | INFO     | trading.asymmetric_env:__init__:435 | [PID:73758] DIMENSION FIX: Parent actually provides 326 dimensions, not 361
2025-06-02 10:45:01.176 | INFO     | trading.asymmetric_env:__init__:446 | [PID:73758] CORRECTED DIMENSIONS: original_state_space=326, enhanced_state_space=376
2025-06-02 10:45:01.176 | INFO     | trading.asymmetric_env:__init__:464 | AsymmetricTradingEnv initialized: stock_dim=10, enhanced_state_space=376, asymmetric_features_per_stock=5
2025-06-02 10:45:01.937 | INFO     | trading.asymmetric_env:reset:714 | [PID:73758] reset: Called. Current day: 0
2025-06-02 10:45:01.937 | INFO     | trading.asymmetric_env:reset:723 | [PID:73758] reset: Reset consecutive_early_failures to 0.
2025-06-02 10:45:01.937 | INFO     | trading.asymmetric_env:reset:730 | [PID:73758] reset: Explicitly set self.day to 0.
2025-06-02 10:45:01.939 | WARNING  | trading.asymmetric_env:reset:794 | [PID:73758] reset: parent_obs from super().reset() is not np.ndarray (type: <class 'list'>). Converting.
2025-06-02 10:45:01.940 | INFO     | trading.asymmetric_env:reset:805 | [PID:73758] reset: Completed. Enhanced state shape: (376,). Day is now 0. self.data is for day 2016-03-15 00:00:00
2025-06-02 10:45:01.990 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 0), bypassing super().step() to prevent division by zero
2025-06-02 10:45:01.991 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 1
2025-06-02 10:45:01.992 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 1
2025-06-02 10:45:01.993 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 1), bypassing super().step() to prevent division by zero
2025-06-02 10:45:01.993 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 2
2025-06-02 10:45:01.994 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 2
2025-06-02 10:45:01.995 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 2), bypassing super().step() to prevent division by zero
2025-06-02 10:45:01.995 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 3
2025-06-02 10:45:01.996 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 3
2025-06-02 10:45:01.997 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 3), bypassing super().step() to prevent division by zero
2025-06-02 10:45:01.997 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 4
2025-06-02 10:45:01.998 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 4
2025-06-02 10:45:01.999 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 4), bypassing super().step() to prevent division by zero
2025-06-02 10:45:01.999 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 5
2025-06-02 10:45:02.000 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 5
2025-06-02 10:45:02.001 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 5), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.001 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 6
2025-06-02 10:45:02.002 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 6
2025-06-02 10:45:02.003 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 6), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.003 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 7
2025-06-02 10:45:02.004 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 7
2025-06-02 10:45:02.005 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 7), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.005 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 8
2025-06-02 10:45:02.006 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 8
2025-06-02 10:45:02.008 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 8), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.008 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 9
2025-06-02 10:45:02.008 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 9
2025-06-02 10:45:02.010 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 9), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.010 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 10
2025-06-02 10:45:02.011 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 10
2025-06-02 10:45:02.012 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 10), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.012 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 11
2025-06-02 10:45:02.013 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 11
2025-06-02 10:45:02.014 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 11), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.015 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 12
2025-06-02 10:45:02.016 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 12
2025-06-02 10:45:02.017 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 12), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.018 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 13
2025-06-02 10:45:02.019 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 13
2025-06-02 10:45:02.020 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 13), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.020 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 14
2025-06-02 10:45:02.021 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 14
2025-06-02 10:45:02.022 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 14), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.022 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 15
2025-06-02 10:45:02.023 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 15
2025-06-02 10:45:02.024 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 15), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.025 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 16
2025-06-02 10:45:02.025 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 16
2025-06-02 10:45:02.027 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 16), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.027 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 17
2025-06-02 10:45:02.028 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 17
2025-06-02 10:45:02.029 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 17), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.029 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 18
2025-06-02 10:45:02.030 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 18
2025-06-02 10:45:02.031 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 18), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.032 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 19
2025-06-02 10:45:02.032 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 19
2025-06-02 10:45:02.034 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 19), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.034 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 20
2025-06-02 10:45:02.035 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:73758] _create_warmup_response: Created safe response for warmup day 20
2025-06-02 10:45:02.036 | INFO     | trading.asymmetric_env:step:830 | [PID:73758] step: Warmup period (day 20), bypassing super().step() to prevent division by zero
2025-06-02 10:45:02.036 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:73758] _create_warmup_response: Advanced day to 21
2025-06-02 10:45:02.037 | INFO     | trading.asymmetric_env:_create_warmup_response:1417 | [PID:73758] _create_warmup_response: Warmup completed after 21 days. Continuing with normal training.
2025-06-02 10:45:02.049 | ERROR    | trading.asymmetric_env:step:940 | [PID:73758] TypeError caught in step(). Error: 'NoneType' object is not subscriptable. super_step_return defined: True, value: None, type: <class 'NoneType'>. Day: 22, Actions: [ 0.09652771 -0.41327187 -0.7240984   0.7188401  -0.05795443  0.49313042
 -0.28848585  0.9839463  -0.29074126 -0.9084143 ]. Traceback:
Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 887, in step
    super_step_return = super().step(actions)
                        ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 343, in step
    end_total_asset = self.state[0] + sum(
                      ~~~~~~~~~~^^^
TypeError: 'NoneType' object is not subscriptable

2025-06-02 10:45:02.049 | CRITICAL | trading.asymmetric_env:step:947 | [PID:73758] CRITICAL: Forcing termination due to TypeError from super().step() to prevent infinite loop. Day: 22
2025-06-02 10:45:02.050 | CRITICAL | trading.asymmetric_env:_create_fallback_response:1482 | [PID:73758] _create_fallback_response: CRITICAL ERROR DETECTED ('TypeError in step: 'NoneType' object is not subscriptable'). Setting self.critical_error_occurred to True.
2025-06-02 10:45:02.105 | WARNING  | trading.asymmetric_env:_calculate_asymmetric_features:660 | [PID:73758] _calculate_asymmetric_features: Insufficient data for AAPL on day 22 (len: 18 < self.WARMUP_DAYS (21)). Using default features.
2025-06-02 10:45:02.106 | ERROR    | trading.asymmetric_env:_create_fallback_response:1580 | [PID:73758] _create_fallback_response: TypeError in step: 'NoneType' object is not subscriptable. Info: {'error': "TypeError in step: 'NoneType' object is not subscriptable", 'day_at_call': 22, 'fallback_response': True, 'actions_at_fallback': [0.09652771055698395, -0.41327187418937683, -0.7240983843803406, 0.7188401222229004, -0.05795442685484886, 0.4931304156780243, -0.2884858548641205, 0.9839463233947754, -0.2907412648200989, -0.9084143042564392], 'super_return_at_fallback_type': "<class 'NoneType'>", 'super_return_at_fallback_repr': 'None'}
2025-06-02 10:45:02.106 | INFO     | trading.asymmetric_env:reset:714 | [PID:73758] reset: Called. Current day: 22
2025-06-02 10:45:02.106 | CRITICAL | trading.asymmetric_env:reset:718 | [PID:73758] reset: Critical error occurred previously. Raising RuntimeError to halt training.
