2025-06-01 23:31:09.003 | INFO     | utils.logging:setup_logging:153 | [PID:51308] Logging initialized - Level: INFO, File: logs\trading_agent_51308.log, Worker ID: 51308
2025-06-01 23:31:09.005 | INFO     | utils.logging:setup_logging:157 | [PID:51308] Worker logging setup complete - Worker ID: 51308
2025-06-01 23:31:09.009 | INFO     | trading.asymmetric_env:__init__:231 | [PID:51308] AsymmetricTradingEnv logger test message
2025-06-01 23:31:09.012 | INFO     | trading.asymmetric_env:__init__:241 | [PID:51308] AsymmetricTradingEnv __init__ (Log Level: INFO, Log File: logs/trading_agent.log, Console: True): Received tech_indicator_list (len: 34): ['SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26', 'RSI_14', 'CCI_20', 'MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'ADX_14', 'DMP_14', 'DMN_14', 'BBL_20_2.0', 'BBM_20_2.0', 'BBU_20_2.0', 'BBB_20_2.0', 'BBP_20_2.0', 'OBV', 'Price_Range', 'Price_Position', 'Returns_1d', 'Returns_5d', 'Returns_20d', 'Volume_MA_20', 'Volume_Ratio', 'Volatility_20d', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime_Numeric']
2025-06-01 23:31:09.013 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-01 23:31:09.014 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-01 23:31:09.015 | INFO     | trading.asymmetric_env:__init__:270 | [PID:51308] Dynamically calculated self.WARMUP_DAYS: 21
2025-06-01 23:31:09.023 | INFO     | strategies.base_strategy:__init__:232 | Strategy initialized: AsymmetricStrategy v1.0.0
2025-06-01 23:31:09.026 | INFO     | strategies.asymmetric_strategy:__init__:78 | Asymmetric strategy initialized with target ratio: 2.0
2025-06-01 23:31:09.029 | WARNING  | trading.asymmetric_env:__init__:340 | [PID:51308] AsymmetricTradingEnv STATE SPACE WARNING (enhanced_state_input=True): Input 'state_space' argument (376) is INCONSISTENT with environment's internal calculation. Based on harmonized stock_dim (10), tech_indicators, and asymmetric_features_size (50), the environment's true_original_state_space is 361, and its calculated enhanced_state_space should be 411. USING ENVIRONMENT'S CALCULATED enhanced_state_space (411) for the agent.
2025-06-01 23:31:09.032 | INFO     | trading.asymmetric_env:__init__:385 | [PID:51308] About to call super().__init__ with parameters:
2025-06-01 23:31:09.034 | INFO     | trading.asymmetric_env:__init__:386 |   df.shape: (17945, 46)
2025-06-01 23:31:09.035 | INFO     | trading.asymmetric_env:__init__:387 |   stock_dim: 10
2025-06-01 23:31:09.046 | INFO     | trading.asymmetric_env:__init__:388 |   hmax: 100
2025-06-01 23:31:09.049 | INFO     | trading.asymmetric_env:__init__:389 |   initial_amount: 100000.0
2025-06-01 23:31:09.052 | INFO     | trading.asymmetric_env:__init__:390 |   num_stock_shares: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
2025-06-01 23:31:09.055 | INFO     | trading.asymmetric_env:__init__:391 |   buy_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-01 23:31:09.057 | INFO     | trading.asymmetric_env:__init__:392 |   sell_cost_pct_list: [0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001, 0.001]
2025-06-01 23:31:09.061 | INFO     | trading.asymmetric_env:__init__:393 |   parent_init_state_space: 361
2025-06-01 23:31:09.063 | INFO     | trading.asymmetric_env:__init__:394 |   action_space: 10
2025-06-01 23:31:09.064 | INFO     | trading.asymmetric_env:__init__:395 |   lowercase_tech_indicator_list: ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-01 23:31:09.068 | INFO     | trading.asymmetric_env:__init__:396 |   reward_scaling: 0.0001
2025-06-01 23:31:09.070 | INFO     | trading.asymmetric_env:__init__:399 | [PID:51308] Calling StockTradingEnv.__init__...
2025-06-01 23:31:09.111 | INFO     | trading.asymmetric_env:__init__:414 | [PID:51308] StockTradingEnv.__init__ completed successfully
2025-06-01 23:31:09.114 | INFO     | trading.asymmetric_env:__init__:425 | [PID:51308] Initialized consecutive_early_failures=0, max_consecutive_early_failures=5
2025-06-01 23:31:09.116 | INFO     | trading.asymmetric_env:__init__:429 | [PID:51308] Initialized self.critical_error_occurred to False.
2025-06-01 23:31:09.117 | INFO     | trading.asymmetric_env:__init__:435 | [PID:51308] DIMENSION FIX: Parent actually provides 326 dimensions, not 361
2025-06-01 23:31:09.119 | INFO     | trading.asymmetric_env:__init__:446 | [PID:51308] CORRECTED DIMENSIONS: original_state_space=326, enhanced_state_space=376
2025-06-01 23:31:09.121 | INFO     | trading.asymmetric_env:__init__:464 | AsymmetricTradingEnv initialized: stock_dim=10, enhanced_state_space=376, asymmetric_features_per_stock=5
2025-06-01 23:31:24.347 | INFO     | trading.asymmetric_env:reset:714 | [PID:51308] reset: Called. Current day: 0
2025-06-01 23:31:24.348 | INFO     | trading.asymmetric_env:reset:723 | [PID:51308] reset: Reset consecutive_early_failures to 0.
2025-06-01 23:31:24.350 | INFO     | trading.asymmetric_env:reset:730 | [PID:51308] reset: Explicitly set self.day to 0.
2025-06-01 23:31:24.389 | WARNING  | trading.asymmetric_env:reset:794 | [PID:51308] reset: parent_obs from super().reset() is not np.ndarray (type: <class 'list'>). Converting.
2025-06-01 23:31:24.392 | INFO     | trading.asymmetric_env:reset:805 | [PID:51308] reset: Completed. Enhanced state shape: (376,). Day is now 0. self.data is for day 2016-03-15 00:00:00
2025-06-01 23:31:24.519 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 0), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.521 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 1
2025-06-01 23:31:24.530 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 1
2025-06-01 23:31:24.539 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 1), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.541 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 2
2025-06-01 23:31:24.548 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 2
2025-06-01 23:31:24.556 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 2), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.558 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 3
2025-06-01 23:31:24.564 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 3
2025-06-01 23:31:24.571 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 3), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.573 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 4
2025-06-01 23:31:24.579 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 4
2025-06-01 23:31:24.591 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 4), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.594 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 5
2025-06-01 23:31:24.600 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 5
2025-06-01 23:31:24.609 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 5), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.613 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 6
2025-06-01 23:31:24.617 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 6
2025-06-01 23:31:24.626 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 6), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.630 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 7
2025-06-01 23:31:24.636 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 7
2025-06-01 23:31:24.648 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 7), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.651 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 8
2025-06-01 23:31:24.656 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 8
2025-06-01 23:31:24.665 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 8), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.668 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 9
2025-06-01 23:31:24.674 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 9
2025-06-01 23:31:24.682 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 9), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.684 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 10
2025-06-01 23:31:24.691 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 10
2025-06-01 23:31:24.705 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 10), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.707 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 11
2025-06-01 23:31:24.715 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 11
2025-06-01 23:31:24.725 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 11), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.726 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 12
2025-06-01 23:31:24.732 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 12
2025-06-01 23:31:24.741 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 12), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.743 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 13
2025-06-01 23:31:24.751 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 13
2025-06-01 23:31:24.759 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 13), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.763 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 14
2025-06-01 23:31:24.768 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 14
2025-06-01 23:31:24.777 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 14), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.779 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 15
2025-06-01 23:31:24.786 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 15
2025-06-01 23:31:24.793 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 15), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.796 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 16
2025-06-01 23:31:24.805 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 16
2025-06-01 23:31:24.816 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 16), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.818 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 17
2025-06-01 23:31:24.825 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 17
2025-06-01 23:31:24.837 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 17), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.839 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 18
2025-06-01 23:31:24.846 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 18
2025-06-01 23:31:24.854 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 18), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.856 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 19
2025-06-01 23:31:24.864 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 19
2025-06-01 23:31:24.874 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 19), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.877 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 20
2025-06-01 23:31:24.884 | INFO     | trading.asymmetric_env:_create_warmup_response:1436 | [PID:51308] _create_warmup_response: Created safe response for warmup day 20
2025-06-01 23:31:24.892 | INFO     | trading.asymmetric_env:step:830 | [PID:51308] step: Warmup period (day 20), bypassing super().step() to prevent division by zero
2025-06-01 23:31:24.895 | INFO     | trading.asymmetric_env:_create_warmup_response:1404 | [PID:51308] _create_warmup_response: Advanced day to 21
2025-06-01 23:31:24.901 | INFO     | trading.asymmetric_env:_create_warmup_response:1417 | [PID:51308] _create_warmup_response: Warmup completed after 21 days. Continuing with normal training.
2025-06-01 23:31:25.761 | ERROR    | trading.asymmetric_env:step:956 | [PID:51308] Exception during super().step(actions) or its unpacking: CRITICAL_ERROR_FINRL_ENV: self.state is None after _update_state() on day 22. Data for day:         date        open  ...  VIX_Regime  vix_regime_numeric
0 2016-06-17   22.082770  ...         0.0                 0.0
1 2016-06-17   97.250000  ...         0.0                 0.0
2 2016-06-17   35.909500  ...         0.0                 0.0
3 2016-06-17   86.911321  ...         0.0                 0.0
4 2016-06-17   12.304525  ...         0.0                 0.0
5 2016-06-17   35.897873  ...         0.0                 0.0
6 2016-06-17  113.883264  ...         0.0                 0.0
7 2016-06-17   44.924909  ...         0.0                 0.0
8 2016-06-17    1.168801  ...         0.0                 0.0
9 2016-06-17   14.520667  ...         0.0                 0.0

[10 rows x 46 columns]. Day: 22, Actions: [ 0.707343    0.7863825  -0.60694766 -0.9482012   0.69274175 -0.11031968
 -0.76200676  0.18470031  0.41076693  0.78196603]
2025-06-01 23:31:25.762 | CRITICAL | trading.asymmetric_env:_create_fallback_response:1482 | [PID:51308] _create_fallback_response: CRITICAL ERROR DETECTED ('Exception in super().step(): CRITICAL_ERROR_FINRL_ENV: self.state is None after _update_state() on day 22. Data for day:         date        open  ...  VIX_Regime  vix_regime_numeric
0 2016-06-17   22.082770  ...         0.0                 0.0
1 2016-06-17   97.250000  ...         0.0                 0.0
2 2016-06-17   35.909500  ...         0.0                 0.0
3 2016-06-17   86.911321  ...         0.0                 0.0
4 2016-06-17   12.304525  ...         0.0                 0.0
5 2016-06-17   35.897873  ...         0.0                 0.0
6 2016-06-17  113.883264  ...         0.0                 0.0
7 2016-06-17   44.924909  ...         0.0                 0.0
8 2016-06-17    1.168801  ...         0.0                 0.0
9 2016-06-17   14.520667  ...         0.0                 0.0

[10 rows x 46 columns]'). Setting self.critical_error_occurred to True.
2025-06-01 23:31:26.308 | WARNING  | trading.asymmetric_env:_calculate_asymmetric_features:660 | [PID:51308] _calculate_asymmetric_features: Insufficient data for AAPL on day 22 (len: 18 < self.WARMUP_DAYS (21)). Using default features.
2025-06-01 23:31:26.310 | ERROR    | trading.asymmetric_env:_create_fallback_response:1580 | [PID:51308] _create_fallback_response: Exception in super().step(): CRITICAL_ERROR_FINRL_ENV: self.state is None after _update_state() on day 22. Data for day:         date        open  ...  VIX_Regime  vix_regime_numeric
0 2016-06-17   22.082770  ...         0.0                 0.0
1 2016-06-17   97.250000  ...         0.0                 0.0
2 2016-06-17   35.909500  ...         0.0                 0.0
3 2016-06-17   86.911321  ...         0.0                 0.0
4 2016-06-17   12.304525  ...         0.0                 0.0
5 2016-06-17   35.897873  ...         0.0                 0.0
6 2016-06-17  113.883264  ...         0.0                 0.0
7 2016-06-17   44.924909  ...         0.0                 0.0
8 2016-06-17    1.168801  ...         0.0                 0.0
9 2016-06-17   14.520667  ...         0.0                 0.0

[10 rows x 46 columns]. Info: {'error': 'Exception in super().step(): CRITICAL_ERROR_FINRL_ENV: self.state is None after _update_state() on day 22. Data for day:         date        open  ...  VIX_Regime  vix_regime_numeric\n0 2016-06-17   22.082770  ...         0.0                 0.0\n1 2016-06-17   97.250000  ...         0.0                 0.0\n2 2016-06-17   35.909500  ...         0.0                 0.0\n3 2016-06-17   86.911321  ...         0.0                 0.0\n4 2016-06-17   12.304525  ...         0.0                 0.0\n5 2016-06-17   35.897873  ...         0.0                 0.0\n6 2016-06-17  113.883264  ...         0.0                 0.0\n7 2016-06-17   44.924909  ...         0.0                 0.0\n8 2016-06-17    1.168801  ...         0.0                 0.0\n9 2016-06-17   14.520667  ...         0.0                 0.0\n\n[10 rows x 46 columns]', 'day_at_call': 22, 'fallback_response': True, 'actions_at_fallback': [0.7073429822921753, 0.7863824963569641, -0.606947660446167, -0.9482011795043945, 0.6927417516708374, -0.11031968146562576, -0.7620067596435547, 0.18470031023025513, 0.41076692938804626, 0.7819660305976868], 'super_return_at_fallback_type': "<class 'NoneType'>", 'super_return_at_fallback_repr': 'None'}
2025-06-01 23:31:26.313 | INFO     | trading.asymmetric_env:reset:714 | [PID:51308] reset: Called. Current day: 22
2025-06-01 23:31:26.315 | CRITICAL | trading.asymmetric_env:reset:718 | [PID:51308] reset: Critical error occurred previously. Raising RuntimeError to halt training.
