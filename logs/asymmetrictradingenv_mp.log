2025-06-07 17:18:15 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:18:15 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:18:15 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:18:15 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:18:15 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:18:15 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:23:03 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:23:03 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:23:03 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:23:03 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:23:03 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:23:03 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:29:25 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:29:25 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:29:25 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:29:25 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:29:25 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:29:25 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:29:33 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)
2025-06-07 17:29:34 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 60, in __init__
    self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/gymnasium/spaces/box.py", line 106, in __init__
    raise TypeError(
TypeError: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)

2025-06-07 17:29:37 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)
2025-06-07 17:29:37 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 60, in __init__
    self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/gymnasium/spaces/box.py", line 106, in __init__
    raise TypeError(
TypeError: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)

2025-06-07 17:29:40 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)
2025-06-07 17:29:40 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 60, in __init__
    self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/gymnasium/spaces/box.py", line 106, in __init__
    raise TypeError(
TypeError: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)

2025-06-07 17:30:41 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:30:41 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:30:41 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:30:41 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:30:41 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:30:41 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:31:12 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:31:12 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:31:12 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:31:12 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:31:12 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:31:12 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:31:30 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:31:30 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:31:30 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:31:30 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:31:30 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:31:30 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:31:37 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)
2025-06-07 17:31:37 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 60, in __init__
    self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/gymnasium/spaces/box.py", line 106, in __init__
    raise TypeError(
TypeError: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)

2025-06-07 17:31:41 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)
2025-06-07 17:31:41 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 60, in __init__
    self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/gymnasium/spaces/box.py", line 106, in __init__
    raise TypeError(
TypeError: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)

2025-06-07 17:31:44 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)
2025-06-07 17:31:44 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 60, in __init__
    self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/gymnasium/spaces/box.py", line 106, in __init__
    raise TypeError(
TypeError: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)

2025-06-07 17:33:42 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:33:42 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:33:42 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:33:42 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:33:42 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:33:42 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:33:49 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)
2025-06-07 17:33:49 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 60, in __init__
    self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/gymnasium/spaces/box.py", line 106, in __init__
    raise TypeError(
TypeError: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)

2025-06-07 17:33:52 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)
2025-06-07 17:33:52 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 60, in __init__
    self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/gymnasium/spaces/box.py", line 106, in __init__
    raise TypeError(
TypeError: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)

2025-06-07 17:33:56 | ERROR    | AsymmetricTradingEnv:__init__:200 | Failed to initialize parent StockTradingEnv: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)
2025-06-07 17:33:56 | ERROR    | AsymmetricTradingEnv:__init__:201 | Traceback: Traceback (most recent call last):
  File "/app/workspaces/finrl-bot/sonet/src/trading/asymmetric_env.py", line 169, in __init__
    super().__init__(
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/finrl/meta/env_stock_trading/env_stocktrading.py", line 60, in __init__
    self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniforge3/envs/finrl-trading-agent/lib/python3.11/site-packages/gymnasium/spaces/box.py", line 106, in __init__
    raise TypeError(
TypeError: Expected all Box shape elements to be integer, actual type=(<class 'gymnasium.spaces.box.Box'>,)

2025-06-07 17:35:46 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:35:46 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:35:46 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:35:46 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:35:46 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:35:46 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:35:54 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:35:54 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:35:54 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:35:58 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:35:58 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:35:58 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:36:01 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:36:01 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:36:01 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:37:58 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:37:58 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:37:58 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:37:58 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:37:58 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:37:58 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:38:06 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:38:06 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:38:06 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:38:10 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:38:10 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:38:10 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:38:14 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:38:14 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:38:14 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:40:41 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:40:41 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:40:41 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:40:41 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:40:41 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:40:41 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:40:49 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:40:49 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:40:49 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:40:53 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:40:53 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:40:53 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:40:56 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:40:56 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:40:56 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:43:30 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:43:30 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:43:30 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:43:30 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:43:30 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:43:30 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:43:38 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:43:38 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:43:38 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:43:43 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:43:43 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:43:43 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:43:48 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:43:48 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:43:48 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:46:29 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:46:29 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:46:29 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:46:29 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:46:29 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:46:29 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:46:36 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:46:36 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:46:36 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:46:41 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:46:41 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:46:41 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:46:47 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:46:47 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:46:47 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:48:30 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:48:30 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:48:30 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:48:30 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:48:30 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:48:30 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:48:38 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:48:38 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:48:38 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:48:44 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:48:44 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:48:44 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:48:51 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:48:51 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:48:51 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:49:43 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:49:43 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:49:43 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:49:43 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:49:43 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:49:43 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:49:53 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:49:53 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:49:53 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:49:58 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:49:58 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:49:58 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:50:06 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:50:06 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:50:06 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:50:52 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:50:52 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:50:52 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:50:52 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:50:52 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:50:52 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:51:01 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:51:01 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:51:01 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:51:07 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:51:07 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:51:07 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:51:24 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:51:24 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:51:24 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:52:49 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:52:49 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:52:49 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:52:49 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:52:49 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:52:49 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:52:57 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:52:57 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:52:57 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:53:02 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:53:02 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:53:02 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:53:09 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:53:09 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:53:09 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:53:42 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:53:42 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:53:42 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:53:42 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:53:42 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:53:42 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:53:52 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:53:52 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:53:52 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:53:58 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:53:58 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:53:58 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
2025-06-07 17:54:05 | INFO     | AsymmetricTradingEnv:__init__:217 | State dimension adjustment: parent=401, asymmetric=30, total=431
2025-06-07 17:54:05 | INFO     | AsymmetricTradingEnv:__init__:249 | AsymmetricTradingEnv initialized with 10 stocks
2025-06-07 17:54:05 | INFO     | AsymmetricTradingEnv:__init__:250 | Asymmetric ratio: 2.0, Volatility lookback: 20
